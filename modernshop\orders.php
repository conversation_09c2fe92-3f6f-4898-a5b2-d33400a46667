<?php
$page_title = "My Orders";
$page_description = "View your order history and track your purchases";

require_once 'config/db-sqlite.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login
require_login();

// Get user's orders
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.name as product_name, p.image as product_image 
        FROM orders o 
        JOIN products p ON o.product_id = p.id 
        WHERE o.user_id = ? 
        ORDER BY o.order_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    $orders = [];
    set_flash_message('Error loading orders.', 'error');
}

include 'includes/header.php';
?>

<div class="container animate-fade-in-up">
    <!-- Page Header -->
    <div class="section-header">
        <h1 class="section-title">
            <i class="fas fa-box"></i>
            My Orders
        </h1>
        <p class="section-subtitle">
            <?php if (empty($orders)): ?>
                You haven't placed any orders yet
            <?php else: ?>
                Track your purchases and view order history
            <?php endif; ?>
        </p>
    </div>
    
    <?php if (empty($orders)): ?>
        <!-- Empty Orders -->
        <div class="text-center animate-fade-in" style="padding: var(--space-20) 0;">
            <div style="font-size: var(--font-size-5xl); color: var(--color-gray-400); margin-bottom: var(--space-6);">
                <i class="fas fa-box-open"></i>
            </div>
            <h3 style="color: var(--color-gray-600); margin-bottom: var(--space-4);">No orders yet</h3>
            <p style="color: var(--color-gray-500); margin-bottom: var(--space-8);">
                When you place your first order, it will appear here.
            </p>
            <a href="index.php" class="btn btn-primary btn-lg hover-lift">
                <i class="fas fa-shopping-bag"></i>
                Start Shopping
            </a>
        </div>
    <?php else: ?>
        <!-- Orders List -->
        <div class="orders-container">
            <?php 
            $current_order_id = null;
            $order_items = [];
            $order_total = 0;
            $order_date = '';
            
            foreach ($orders as $index => $order): 
                // Group items by order (assuming orders with same date are part of same order)
                if ($current_order_id === null || $order['order_date'] !== $order_date) {
                    // Display previous order if exists
                    if ($current_order_id !== null) {
                        displayOrderCard($order_items, $order_total, $order_date, $current_order_id);
                        $order_items = [];
                        $order_total = 0;
                    }
                    
                    $current_order_id = $order['id'];
                    $order_date = $order['order_date'];
                }
                
                $order_items[] = $order;
                $order_total += $order['total_price'];
                
                // Display last order
                if ($index === count($orders) - 1) {
                    displayOrderCard($order_items, $order_total, $order_date, $current_order_id);
                }
            endforeach;
            ?>
        </div>
    <?php endif; ?>
</div>

<?php
function displayOrderCard($items, $total, $date, $order_id) {
    $formatted_date = date('F j, Y', strtotime($date));
    $order_number = str_pad($order_id, 6, '0', STR_PAD_LEFT);
    ?>
    <div class="card order-card animate-fade-in-up hover-lift" style="margin-bottom: var(--space-6);">
        <!-- Order Header -->
        <div class="card-header" style="background: var(--color-gray-50);">
            <div class="flex justify-between items-center">
                <div>
                    <h3 style="margin: 0; color: var(--color-gray-900);">
                        Order #<?php echo $order_number; ?>
                    </h3>
                    <p style="margin: var(--space-1) 0 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                        Placed on <?php echo $formatted_date; ?>
                    </p>
                </div>
                <div class="text-right">
                    <div style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                        <?php echo format_price($total); ?>
                    </div>
                    <div class="badge badge-success" style="margin-top: var(--space-1);">
                        <i class="fas fa-check"></i>
                        Completed
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Items -->
        <div class="card-body">
            <div style="display: grid; gap: var(--space-4);">
                <?php foreach ($items as $item): ?>
                    <div class="order-item-row" style="display: flex; gap: var(--space-4); align-items: center; padding: var(--space-3); border: 1px solid var(--color-gray-200); border-radius: var(--radius-md); transition: all var(--transition-normal);">
                        <!-- Product Image -->
                        <div class="flex-shrink-0">
                            <img 
                                src="<?php echo get_product_image($item['product_image']); ?>" 
                                alt="<?php echo htmlspecialchars($item['product_name']); ?>"
                                style="width: 80px; height: 80px; object-fit: cover; border-radius: var(--radius-md);"
                            >
                        </div>
                        
                        <!-- Product Details -->
                        <div class="flex-1">
                            <h4 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-2); color: var(--color-gray-900);">
                                <?php echo htmlspecialchars($item['product_name']); ?>
                            </h4>
                            <div style="display: flex; gap: var(--space-4); align-items: center; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                <span>
                                    <i class="fas fa-cube"></i>
                                    Quantity: <?php echo $item['quantity']; ?>
                                </span>
                                <span>
                                    <i class="fas fa-dollar-sign"></i>
                                    <?php echo format_price($item['total_price'] / $item['quantity']); ?> each
                                </span>
                            </div>
                        </div>
                        
                        <!-- Item Total -->
                        <div class="text-right">
                            <div style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                                <?php echo format_price($item['total_price']); ?>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col gap-2">
                            <a href="product.php?id=<?php echo $item['product_id']; ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-eye"></i>
                                View
                            </a>
                            <button class="btn btn-outline btn-sm" onclick="reorderItem(<?php echo $item['product_id']; ?>, <?php echo $item['quantity']; ?>)">
                                <i class="fas fa-redo"></i>
                                Reorder
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Order Footer -->
        <div class="card-footer" style="background: var(--color-gray-50);">
            <div class="flex justify-between items-center">
                <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);">
                    <i class="fas fa-truck"></i>
                    Delivered on <?php echo date('F j, Y', strtotime($date . ' +5 days')); ?>
                </div>
                <div class="flex gap-3">
                    <button class="btn btn-secondary btn-sm" onclick="downloadInvoice(<?php echo $order_id; ?>)">
                        <i class="fas fa-download"></i>
                        Invoice
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="trackOrder(<?php echo $order_id; ?>)">
                        <i class="fas fa-map-marker-alt"></i>
                        Track Order
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php
}

include 'includes/footer.php';
?>

<script>
// Reorder item function
function reorderItem(productId, quantity) {
    if (confirm(`Add ${quantity} item(s) to cart?`)) {
        fetch('cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=add&product_id=${productId}&quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Item added to cart successfully!', 'success');
                
                // Update cart count
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = data.cart_count;
                    cartCount.style.display = 'block';
                }
            } else {
                showNotification(data.message || 'Error adding item to cart', 'error');
            }
        })
        .catch(error => {
            showNotification('Error adding item to cart', 'error');
        });
    }
}

// Download invoice function
function downloadInvoice(orderId) {
    showNotification('Invoice download will be available soon', 'info');
}

// Track order function
function trackOrder(orderId) {
    showNotification('Order tracking will be available soon', 'info');
}

// Add hover effects to order items
document.addEventListener('DOMContentLoaded', function() {
    const orderItems = document.querySelectorAll('.order-item-row');
    
    orderItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.borderColor = 'var(--color-primary)';
            this.style.backgroundColor = 'var(--color-primary-50)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.borderColor = 'var(--color-gray-200)';
            this.style.backgroundColor = 'transparent';
        });
    });
    
    // Stagger animation for order cards
    const orderCards = document.querySelectorAll('.order-card');
    orderCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.animation = `fadeInUp 0.6s ease-out forwards`;
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
</script>
