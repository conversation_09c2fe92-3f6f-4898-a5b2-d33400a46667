/* Component Library - Reusable UI Components */

/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-white);
    border-color: var(--color-primary);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-accent-purple) 100%);
    border-color: var(--color-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--color-white);
    color: var(--color-gray-700);
    border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--color-gray-50);
    border-color: var(--color-gray-400);
}

.btn-success {
    background-color: var(--color-success);
    color: var(--color-white);
    border-color: var(--color-success);
}

.btn-success:hover:not(:disabled) {
    background-color: var(--color-success-dark);
    border-color: var(--color-success-dark);
}

.btn-danger {
    background-color: var(--color-error);
    color: var(--color-white);
    border-color: var(--color-error);
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--color-error-dark);
    border-color: var(--color-error-dark);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: var(--color-white);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--font-size-xl);
}

/* Card Components */
.card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
    border-color: var(--color-primary);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    background-color: var(--color-background-tertiary);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
    background-color: var(--color-background-tertiary);
}

/* Product Card Specific */
.product-card {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent-purple));
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.product-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-6px) scale(1.02);
    border-color: var(--color-primary);
}

.product-card:hover::before {
    opacity: 0.05;
}

.product-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: var(--color-gray-100);
}

.product-card-content {
    padding: var(--space-4);
}

.product-card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-2);
}

.product-card-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-3);
}

/* Badge Components */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background-color: var(--color-primary-100);
    color: var(--color-primary-700);
}

.badge-success {
    background-color: var(--color-success-100);
    color: var(--color-success-700);
}

.badge-warning {
    background-color: var(--color-warning-100);
    color: var(--color-warning-700);
}

.badge-error {
    background-color: var(--color-error-100);
    color: var(--color-error-700);
}

/* Alert Components */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-md);
    border: 1px solid;
    margin-bottom: var(--space-4);
}

.alert-success {
    background-color: var(--color-success-50);
    border-color: var(--color-success-200);
    color: var(--color-success-800);
}

.alert-warning {
    background-color: var(--color-warning-50);
    border-color: var(--color-warning-200);
    color: var(--color-warning-800);
}

.alert-error {
    background-color: var(--color-error-50);
    border-color: var(--color-error-200);
    color: var(--color-error-800);
}

.alert-info {
    background-color: var(--color-primary-50);
    border-color: var(--color-primary-200);
    color: var(--color-primary-800);
}

/* Loading Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-gray-200);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
