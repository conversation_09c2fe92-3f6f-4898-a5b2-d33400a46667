<?php
// Analytics and Tracking Functions

// Event tracking
class Analytics {
    private $pdo;
    private $sessionId;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->sessionId = session_id();
    }
    
    public function trackEvent($event, $category = 'general', $data = []) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO analytics_events (session_id, user_id, event, category, data, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $this->sessionId,
                $_SESSION['user_id'] ?? null,
                $event,
                $category,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Analytics error: " . $e->getMessage());
            return false;
        }
    }
    
    public function trackPageView($page, $title = '') {
        return $this->trackEvent('page_view', 'navigation', [
            'page' => $page,
            'title' => $title,
            'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
            'timestamp' => time()
        ]);
    }
    
    public function trackPurchase($orderId, $amount, $items = []) {
        return $this->trackEvent('purchase', 'ecommerce', [
            'order_id' => $orderId,
            'amount' => $amount,
            'items' => $items,
            'currency' => 'USD'
        ]);
    }
    
    public function trackAddToCart($productId, $quantity = 1, $price = 0) {
        return $this->trackEvent('add_to_cart', 'ecommerce', [
            'product_id' => $productId,
            'quantity' => $quantity,
            'price' => $price
        ]);
    }
    
    public function trackSearch($query, $results = 0) {
        return $this->trackEvent('search', 'engagement', [
            'query' => $query,
            'results' => $results
        ]);
    }
    
    public function getPopularProducts($limit = 10, $days = 30) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    JSON_EXTRACT(data, '$.product_id') as product_id,
                    COUNT(*) as views,
                    p.name,
                    p.price
                FROM analytics_events ae
                LEFT JOIN products p ON p.id = JSON_EXTRACT(ae.data, '$.product_id')
                WHERE ae.event = 'product_view' 
                AND ae.created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
                AND JSON_EXTRACT(data, '$.product_id') IS NOT NULL
                GROUP BY JSON_EXTRACT(data, '$.product_id')
                ORDER BY views DESC
                LIMIT ?
            ");
            
            $stmt->execute([$days, $limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Analytics query error: " . $e->getMessage());
            return [];
        }
    }
    
    public function getSearchTerms($limit = 20, $days = 30) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    JSON_EXTRACT(data, '$.query') as search_term,
                    COUNT(*) as searches,
                    AVG(JSON_EXTRACT(data, '$.results')) as avg_results
                FROM analytics_events 
                WHERE event = 'search' 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY JSON_EXTRACT(data, '$.query')
                ORDER BY searches DESC
                LIMIT ?
            ");
            
            $stmt->execute([$days, $limit]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Analytics query error: " . $e->getMessage());
            return [];
        }
    }
    
    public function getDashboardStats($days = 30) {
        try {
            $stats = [];
            
            // Page views
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM analytics_events 
                WHERE event = 'page_view' 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            $stats['page_views'] = $stmt->fetch()['count'];
            
            // Unique visitors
            $stmt = $this->pdo->prepare("
                SELECT COUNT(DISTINCT session_id) as count 
                FROM analytics_events 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            $stats['unique_visitors'] = $stmt->fetch()['count'];
            
            // Purchases
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count, SUM(JSON_EXTRACT(data, '$.amount')) as revenue
                FROM analytics_events 
                WHERE event = 'purchase' 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            $result = $stmt->fetch();
            $stats['purchases'] = $result['count'];
            $stats['revenue'] = $result['revenue'] ?? 0;
            
            // Cart additions
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM analytics_events 
                WHERE event = 'add_to_cart' 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            $stats['cart_additions'] = $stmt->fetch()['count'];
            
            return $stats;
        } catch (Exception $e) {
            error_log("Analytics stats error: " . $e->getMessage());
            return [];
        }
    }
}

// A/B Testing
class ABTest {
    private $pdo;
    private $tests = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadActiveTests();
    }
    
    private function loadActiveTests() {
        try {
            $stmt = $this->pdo->query("
                SELECT * FROM ab_tests 
                WHERE status = 'active' 
                AND start_date <= NOW() 
                AND (end_date IS NULL OR end_date >= NOW())
            ");
            
            while ($test = $stmt->fetch()) {
                $this->tests[$test['name']] = $test;
            }
        } catch (Exception $e) {
            error_log("A/B Test loading error: " . $e->getMessage());
        }
    }
    
    public function getVariant($testName, $userId = null) {
        if (!isset($this->tests[$testName])) {
            return 'control';
        }
        
        $test = $this->tests[$testName];
        $variants = json_decode($test['variants'], true);
        
        // Use user ID or session ID for consistent assignment
        $identifier = $userId ?: session_id();
        $hash = crc32($testName . $identifier);
        $bucket = abs($hash) % 100;
        
        $cumulative = 0;
        foreach ($variants as $variant => $percentage) {
            $cumulative += $percentage;
            if ($bucket < $cumulative) {
                $this->trackAssignment($testName, $variant, $identifier);
                return $variant;
            }
        }
        
        return 'control';
    }
    
    private function trackAssignment($testName, $variant, $identifier) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT IGNORE INTO ab_test_assignments (test_name, variant, identifier) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$testName, $variant, $identifier]);
        } catch (Exception $e) {
            error_log("A/B Test assignment error: " . $e->getMessage());
        }
    }
    
    public function trackConversion($testName, $identifier = null) {
        $identifier = $identifier ?: session_id();
        
        try {
            $stmt = $this->pdo->prepare("
                UPDATE ab_test_assignments 
                SET converted = 1, conversion_date = NOW() 
                WHERE test_name = ? AND identifier = ? AND converted = 0
            ");
            $stmt->execute([$testName, $identifier]);
        } catch (Exception $e) {
            error_log("A/B Test conversion error: " . $e->getMessage());
        }
    }
}

// User behavior tracking
function trackUserBehavior($action, $data = []) {
    if (!isset($_SESSION['user_behavior'])) {
        $_SESSION['user_behavior'] = [];
    }
    
    $_SESSION['user_behavior'][] = [
        'action' => $action,
        'data' => $data,
        'timestamp' => time(),
        'page' => $_SERVER['REQUEST_URI'] ?? ''
    ];
    
    // Keep only last 50 actions to prevent session bloat
    if (count($_SESSION['user_behavior']) > 50) {
        $_SESSION['user_behavior'] = array_slice($_SESSION['user_behavior'], -50);
    }
}

// Heat map data collection
function collectHeatmapData($x, $y, $element = '') {
    if (!isset($_SESSION['heatmap_data'])) {
        $_SESSION['heatmap_data'] = [];
    }
    
    $_SESSION['heatmap_data'][] = [
        'x' => $x,
        'y' => $y,
        'element' => $element,
        'timestamp' => time(),
        'page' => $_SERVER['REQUEST_URI'] ?? ''
    ];
}

// Google Analytics integration
function renderGoogleAnalytics($trackingId) {
    if (empty($trackingId)) {
        return '';
    }
    
    return "
    <!-- Google Analytics -->
    <script async src=\"https://www.googletagmanager.com/gtag/js?id={$trackingId}\"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{$trackingId}', {
            page_title: document.title,
            page_location: window.location.href
        });
        
        // Enhanced ecommerce tracking
        function trackPurchase(transactionId, value, items) {
            gtag('event', 'purchase', {
                transaction_id: transactionId,
                value: value,
                currency: 'USD',
                items: items
            });
        }
        
        function trackAddToCart(itemId, itemName, category, quantity, price) {
            gtag('event', 'add_to_cart', {
                currency: 'USD',
                value: price * quantity,
                items: [{
                    item_id: itemId,
                    item_name: itemName,
                    category: category,
                    quantity: quantity,
                    price: price
                }]
            });
        }
        
        function trackViewItem(itemId, itemName, category, price) {
            gtag('event', 'view_item', {
                currency: 'USD',
                value: price,
                items: [{
                    item_id: itemId,
                    item_name: itemName,
                    category: category,
                    price: price
                }]
            });
        }
    </script>
    ";
}

// Facebook Pixel integration
function renderFacebookPixel($pixelId) {
    if (empty($pixelId)) {
        return '';
    }
    
    return "
    <!-- Facebook Pixel -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{$pixelId}');
        fbq('track', 'PageView');
        
        function trackFBPurchase(value, currency = 'USD') {
            fbq('track', 'Purchase', {value: value, currency: currency});
        }
        
        function trackFBAddToCart(value, currency = 'USD') {
            fbq('track', 'AddToCart', {value: value, currency: currency});
        }
    </script>
    <noscript>
        <img height=\"1\" width=\"1\" style=\"display:none\" 
             src=\"https://www.facebook.com/tr?id={$pixelId}&ev=PageView&noscript=1\"/>
    </noscript>
    ";
}

// Initialize analytics
function initializeAnalytics($pdo, $config = []) {
    $analytics = new Analytics($pdo);
    $abTest = new ABTest($pdo);
    
    // Track page view
    $page = $_SERVER['REQUEST_URI'] ?? '';
    $title = $config['page_title'] ?? '';
    $analytics->trackPageView($page, $title);
    
    return [
        'analytics' => $analytics,
        'ab_test' => $abTest
    ];
}
?>
