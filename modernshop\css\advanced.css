/* Advanced Features & Enhancements */

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --color-white: #1a1a1a;
        --color-gray-50: #262626;
        --color-gray-100: #404040;
        --color-gray-200: #525252;
        --color-gray-300: #737373;
        --color-gray-400: #a3a3a3;
        --color-gray-500: #d4d4d4;
        --color-gray-600: #e5e5e5;
        --color-gray-700: #f5f5f5;
        --color-gray-800: #fafafa;
        --color-gray-900: #ffffff;
    }
    
    .card, .form-container, .admin-form, .data-table-container {
        background: var(--color-gray-100);
        border-color: var(--color-gray-300);
    }
    
    .site-header, .admin-sidebar {
        background: var(--color-gray-100);
        border-color: var(--color-gray-300);
    }
}

/* Advanced Loading States - Skeleton animations removed */

/* Advanced Search */
.search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: var(--space-4) var(--space-6) var(--space-4) var(--space-12);
    border: 2px solid var(--color-gray-300);
    border-radius: var(--radius-full);
    font-size: var(--font-size-lg);
    background: var(--color-white);
    transition: all var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
    transform: scale(1.02);
}

.search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-gray-500);
    font-size: var(--font-size-lg);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-dropdown);
    max-height: 400px;
    overflow-y: auto;
    margin-top: var(--space-2);
    display: none;
}

.search-suggestions.show {
    display: block;
    animation: fadeInUp 0.2s ease-out;
}

.search-suggestion {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--color-gray-100);
    cursor: pointer;
    transition: background var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.search-suggestion:hover {
    background: var(--color-gray-50);
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion-icon {
    color: var(--color-gray-400);
}

.search-suggestion-text {
    flex: 1;
}

.search-suggestion-category {
    font-size: var(--font-size-xs);
    color: var(--color-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Advanced Filters */
.filter-panel {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    margin-bottom: var(--space-6);
}

.filter-section {
    margin-bottom: var(--space-6);
}

.filter-section:last-child {
    margin-bottom: 0;
}

.filter-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.filter-option {
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-full);
    background: var(--color-white);
    color: var(--color-gray-700);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.filter-option:hover {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
    color: var(--color-primary);
}

.filter-option.active {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-white);
}

.price-range {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.price-input {
    width: 100px;
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    text-align: center;
}

/* Advanced Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-2);
    margin: var(--space-8) 0;
}

.pagination-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--color-gray-300);
    background: var(--color-white);
    color: var(--color-gray-700);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.pagination-btn:hover {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
    color: var(--color-primary);
}

.pagination-btn.active {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    margin: 0 var(--space-4);
    color: var(--color-gray-600);
    font-size: var(--font-size-sm);
}

/* Advanced Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-gray-900);
    color: var(--color-white);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: var(--z-tooltip);
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--color-gray-900);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

/* Advanced Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--color-gray-500);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--color-gray-100);
    color: var(--color-gray-700);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Advanced Progress Bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--color-gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Advanced Tabs */
.tabs {
    border-bottom: 1px solid var(--color-gray-200);
    margin-bottom: var(--space-6);
}

.tab-list {
    display: flex;
    gap: var(--space-1);
}

.tab-button {
    padding: var(--space-3) var(--space-6);
    border: none;
    background: none;
    color: var(--color-gray-600);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    transition: all var(--transition-fast);
    position: relative;
}

.tab-button:hover {
    color: var(--color-primary);
    background: var(--color-gray-50);
}

.tab-button.active {
    color: var(--color-primary);
    background: var(--color-white);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card, .admin-form {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}
