<?php
// Performance Optimization Functions

// Page caching
class PageCache {
    private $cacheDir;
    private $cacheTime;
    
    public function __construct($cacheDir = 'cache', $cacheTime = 3600) {
        $this->cacheDir = $cacheDir;
        $this->cacheTime = $cacheTime;
        
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    public function get($key) {
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            return false;
        }
        
        $data = file_get_contents($filename);
        $cache = json_decode($data, true);
        
        if (!$cache || time() > $cache['expires']) {
            unlink($filename);
            return false;
        }
        
        return $cache['content'];
    }
    
    public function set($key, $content, $ttl = null) {
        $ttl = $ttl ?: $this->cacheTime;
        $filename = $this->getCacheFilename($key);
        
        $cache = [
            'content' => $content,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($filename, json_encode($cache)) !== false;
    }
    
    public function delete($key) {
        $filename = $this->getCacheFilename($key);
        if (file_exists($filename)) {
            return unlink($filename);
        }
        return true;
    }
    
    public function clear() {
        $files = glob($this->cacheDir . '/*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
        return true;
    }
    
    private function getCacheFilename($key) {
        return $this->cacheDir . '/' . md5($key) . '.cache';
    }
}

// Database query caching
class QueryCache {
    private static $cache = [];
    private static $enabled = true;
    
    public static function get($query, $params = []) {
        if (!self::$enabled) {
            return false;
        }
        
        $key = self::getCacheKey($query, $params);
        return isset(self::$cache[$key]) ? self::$cache[$key] : false;
    }
    
    public static function set($query, $params, $result) {
        if (!self::$enabled) {
            return;
        }
        
        $key = self::getCacheKey($query, $params);
        self::$cache[$key] = $result;
    }
    
    public static function clear() {
        self::$cache = [];
    }
    
    public static function disable() {
        self::$enabled = false;
    }
    
    public static function enable() {
        self::$enabled = true;
    }
    
    private static function getCacheKey($query, $params) {
        return md5($query . serialize($params));
    }
}

// Image optimization
function optimizeImage($sourcePath, $destinationPath, $quality = 85, $maxWidth = 1200, $maxHeight = 1200) {
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        return false;
    }
    
    $mimeType = $imageInfo['mime'];
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    
    // Calculate new dimensions
    $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
    if ($ratio >= 1) {
        $ratio = 1; // Don't upscale
    }
    
    $newWidth = intval($originalWidth * $ratio);
    $newHeight = intval($originalHeight * $ratio);
    
    // Create image resource
    switch ($mimeType) {
        case 'image/jpeg':
            $source = imagecreatefromjpeg($sourcePath);
            break;
        case 'image/png':
            $source = imagecreatefrompng($sourcePath);
            break;
        case 'image/gif':
            $source = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    if (!$source) {
        return false;
    }
    
    // Create new image
    $destination = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
        imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Resize image
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Save optimized image
    $result = false;
    switch ($mimeType) {
        case 'image/jpeg':
            $result = imagejpeg($destination, $destinationPath, $quality);
            break;
        case 'image/png':
            $result = imagepng($destination, $destinationPath, 9);
            break;
        case 'image/gif':
            $result = imagegif($destination, $destinationPath);
            break;
    }
    
    // Clean up
    imagedestroy($source);
    imagedestroy($destination);
    
    return $result;
}

// CSS and JS minification
function minifyCSS($css) {
    // Remove comments
    $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
    
    // Remove unnecessary whitespace
    $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
    
    // Remove trailing semicolon before closing brace
    $css = str_replace(';}', '}', $css);
    
    return trim($css);
}

function minifyJS($js) {
    // Remove single-line comments (but preserve URLs)
    $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
    
    // Remove multi-line comments
    $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
    
    // Remove unnecessary whitespace
    $js = preg_replace('/\s+/', ' ', $js);
    
    // Remove whitespace around operators
    $js = preg_replace('/\s*([{}();,:])\s*/', '$1', $js);
    
    return trim($js);
}

// Gzip compression
function enableGzipCompression() {
    if (!headers_sent() && extension_loaded('zlib') && !ini_get('zlib.output_compression')) {
        if (strpos($_SERVER['HTTP_ACCEPT_ENCODING'] ?? '', 'gzip') !== false) {
            ob_start('ob_gzhandler');
        }
    }
}

// Browser caching headers
function setBrowserCacheHeaders($type = 'static', $maxAge = 86400) {
    $etag = md5($_SERVER['REQUEST_URI'] . filemtime($_SERVER['SCRIPT_FILENAME']));
    
    header("ETag: \"$etag\"");
    header("Cache-Control: public, max-age=$maxAge");
    header("Expires: " . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');
    
    // Check if client has cached version
    $clientEtag = $_SERVER['HTTP_IF_NONE_MATCH'] ?? '';
    if ($clientEtag === "\"$etag\"") {
        http_response_code(304);
        exit;
    }
}

// Database connection pooling simulation
class DatabasePool {
    private static $connections = [];
    private static $maxConnections = 5;
    private static $currentConnections = 0;
    
    public static function getConnection($dsn, $username, $password, $options = []) {
        $key = md5($dsn . $username);
        
        if (isset(self::$connections[$key]) && self::$connections[$key]['count'] < self::$maxConnections) {
            self::$connections[$key]['count']++;
            return self::$connections[$key]['pdo'];
        }
        
        if (self::$currentConnections >= self::$maxConnections) {
            throw new Exception('Maximum database connections reached');
        }
        
        $pdo = new PDO($dsn, $username, $password, $options);
        
        self::$connections[$key] = [
            'pdo' => $pdo,
            'count' => 1
        ];
        
        self::$currentConnections++;
        
        return $pdo;
    }
    
    public static function releaseConnection($pdo) {
        foreach (self::$connections as $key => &$connection) {
            if ($connection['pdo'] === $pdo) {
                $connection['count']--;
                if ($connection['count'] <= 0) {
                    unset(self::$connections[$key]);
                    self::$currentConnections--;
                }
                break;
            }
        }
    }
}

// Performance monitoring
class PerformanceMonitor {
    private static $startTime;
    private static $memoryStart;
    private static $queries = [];
    
    public static function start() {
        self::$startTime = microtime(true);
        self::$memoryStart = memory_get_usage();
    }
    
    public static function logQuery($query, $executionTime) {
        self::$queries[] = [
            'query' => $query,
            'time' => $executionTime,
            'memory' => memory_get_usage()
        ];
    }
    
    public static function getStats() {
        $endTime = microtime(true);
        $memoryEnd = memory_get_usage();
        $memoryPeak = memory_get_peak_usage();
        
        return [
            'execution_time' => round(($endTime - self::$startTime) * 1000, 2),
            'memory_used' => round(($memoryEnd - self::$memoryStart) / 1024 / 1024, 2),
            'memory_peak' => round($memoryPeak / 1024 / 1024, 2),
            'query_count' => count(self::$queries),
            'queries' => self::$queries
        ];
    }
    
    public static function outputStats() {
        if (isset($_GET['debug']) && $_GET['debug'] === 'performance') {
            $stats = self::getStats();
            echo "<!-- Performance Stats:\n";
            echo "Execution Time: {$stats['execution_time']}ms\n";
            echo "Memory Used: {$stats['memory_used']}MB\n";
            echo "Peak Memory: {$stats['memory_peak']}MB\n";
            echo "Database Queries: {$stats['query_count']}\n";
            echo "-->";
        }
    }
}

// Lazy loading implementation
function generateLazyImage($src, $alt = '', $class = '', $width = null, $height = null) {
    $placeholder = 'data:image/svg+xml;base64,' . base64_encode(
        '<svg width="' . ($width ?: 400) . '" height="' . ($height ?: 300) . '" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f3f4f6"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af">Loading...</text>
        </svg>'
    );
    
    $attributes = [
        'src' => $placeholder,
        'data-src' => $src,
        'alt' => $alt,
        'class' => trim($class . ' lazy'),
        'loading' => 'lazy'
    ];
    
    if ($width) $attributes['width'] = $width;
    if ($height) $attributes['height'] = $height;
    
    $attributeString = '';
    foreach ($attributes as $key => $value) {
        $attributeString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
    }
    
    return '<img' . $attributeString . '>';
}

// Critical CSS extraction
function extractCriticalCSS($html, $css) {
    // Simple critical CSS extraction (in production, use tools like Critical or Penthouse)
    $criticalSelectors = [
        'body', 'html', 'header', 'nav', 'main', 'footer',
        '.container', '.btn', '.card', '.hero',
        'h1', 'h2', 'h3', 'p', 'a'
    ];
    
    $criticalCSS = '';
    $cssRules = explode('}', $css);
    
    foreach ($cssRules as $rule) {
        foreach ($criticalSelectors as $selector) {
            if (strpos($rule, $selector) !== false) {
                $criticalCSS .= $rule . '}';
                break;
            }
        }
    }
    
    return $criticalCSS;
}

// Initialize performance optimizations
function initializePerformance() {
    // Start performance monitoring
    PerformanceMonitor::start();
    
    // Enable gzip compression
    enableGzipCompression();
    
    // Set browser cache headers for static assets
    if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/', $_SERVER['REQUEST_URI'])) {
        setBrowserCacheHeaders('static', 2592000); // 30 days
    }
    
    // Register shutdown function to output performance stats
    register_shutdown_function(['PerformanceMonitor', 'outputStats']);
}
?>
