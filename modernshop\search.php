<?php
$page_title = "Search Results";
$page_description = "Find the perfect products for you";
$additional_css = ['css/advanced.css', 'css/animations.css'];
$additional_js = ['js/advanced.js'];

require_once 'config/db-sqlite.php';
require_once 'includes/functions.php';
require_once 'includes/performance.php';
require_once 'includes/analytics.php';

// Initialize performance monitoring
initializePerformance();

// Initialize analytics
$analyticsData = initializeAnalytics($pdo, ['page_title' => $page_title]);
$analytics = $analyticsData['analytics'];

// Get search parameters
$search_query = sanitize_input($_GET['search'] ?? '');
$category_filter = (int)($_GET['category'] ?? 0);
$min_price = (float)($_GET['min_price'] ?? 0);
$max_price = (float)($_GET['max_price'] ?? 0);
$sort_by = sanitize_input($_GET['sort'] ?? 'relevance');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build search query
$where_conditions = [];
$params = [];

if (!empty($search_query)) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $search_term = "%{$search_query}%";
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($category_filter > 0) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_filter;
}

if ($min_price > 0) {
    $where_conditions[] = "p.price >= ?";
    $params[] = $min_price;
}

if ($max_price > 0) {
    $where_conditions[] = "p.price <= ?";
    $params[] = $max_price;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Determine sort order
$order_clause = 'ORDER BY ';
switch ($sort_by) {
    case 'price_low':
        $order_clause .= 'p.price ASC';
        break;
    case 'price_high':
        $order_clause .= 'p.price DESC';
        break;
    case 'name':
        $order_clause .= 'p.name ASC';
        break;
    case 'newest':
        $order_clause .= 'p.id DESC';
        break;
    default:
        $order_clause .= 'p.id DESC';
}

try {
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id {$where_clause}";
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($params);
    $total_results = $stmt->fetch()['total'];
    
    // Get products
    $query = "
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        {$where_clause} 
        {$order_clause} 
        LIMIT {$per_page} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    // Track search
    if (!empty($search_query)) {
        $analytics->trackSearch($search_query, $total_results);
    }
    
} catch (Exception $e) {
    $products = [];
    $total_results = 0;
    set_flash_message('Error performing search.', 'error');
}

// Get categories for filter
try {
    $categories_stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $categories_stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
}

// Calculate pagination
$total_pages = ceil($total_results / $per_page);

include 'includes/header.php';
?>

<div class="container animate-fade-in-up">
    <!-- Search Header -->
    <div class="section-header">
        <h1 class="section-title">
            <i class="fas fa-search"></i>
            <?php if (!empty($search_query)): ?>
                Search Results for "<?php echo htmlspecialchars($search_query); ?>"
            <?php else: ?>
                Browse Products
            <?php endif; ?>
        </h1>
        <p class="section-subtitle">
            <?php if ($total_results > 0): ?>
                Found <?php echo number_format($total_results); ?> product<?php echo $total_results != 1 ? 's' : ''; ?>
            <?php else: ?>
                No products found matching your criteria
            <?php endif; ?>
        </p>
    </div>
    
    <!-- Advanced Search -->
    <div class="search-container animate-fade-in" style="margin-bottom: var(--space-8);">
        <form method="GET" action="search.php">
            <div class="search-input-container" style="position: relative;">
                <i class="fas fa-search search-icon"></i>
                <input 
                    type="text" 
                    name="search" 
                    class="search-input" 
                    placeholder="Search for products..." 
                    value="<?php echo htmlspecialchars($search_query); ?>"
                    autocomplete="off"
                >
                <div class="search-suggestions"></div>
            </div>
        </form>
    </div>
    
    <div class="grid lg:grid-cols-4 gap-8">
        <!-- Filters Sidebar -->
        <div class="lg:col-span-1">
            <div class="filter-panel animate-fade-in-left">
                <form method="GET" action="search.php" id="filter-form">
                    <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_query); ?>">
                    
                    <!-- Category Filter -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i class="fas fa-tags"></i>
                            Categories
                        </h3>
                        <div class="filter-options">
                            <div class="filter-option <?php echo $category_filter == 0 ? 'active' : ''; ?>" 
                                 data-type="category" data-value="0">
                                All Categories
                            </div>
                            <?php foreach ($categories as $category): ?>
                                <div class="filter-option <?php echo $category_filter == $category['id'] ? 'active' : ''; ?>" 
                                     data-type="category" data-value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Price Filter -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i class="fas fa-dollar-sign"></i>
                            Price Range
                        </h3>
                        <div class="price-range">
                            <input 
                                type="number" 
                                name="min_price" 
                                class="price-input" 
                                placeholder="Min" 
                                value="<?php echo $min_price > 0 ? $min_price : ''; ?>"
                                min="0"
                                step="0.01"
                            >
                            <span>to</span>
                            <input 
                                type="number" 
                                name="max_price" 
                                class="price-input" 
                                placeholder="Max" 
                                value="<?php echo $max_price > 0 ? $max_price : ''; ?>"
                                min="0"
                                step="0.01"
                            >
                        </div>
                    </div>
                    
                    <!-- Sort Options -->
                    <div class="filter-section">
                        <h3 class="filter-title">
                            <i class="fas fa-sort"></i>
                            Sort By
                        </h3>
                        <select name="sort" class="form-select" onchange="this.form.submit()">
                            <option value="relevance" <?php echo $sort_by == 'relevance' ? 'selected' : ''; ?>>Relevance</option>
                            <option value="price_low" <?php echo $sort_by == 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                            <option value="price_high" <?php echo $sort_by == 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                            <option value="name" <?php echo $sort_by == 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                            <option value="newest" <?php echo $sort_by == 'newest' ? 'selected' : ''; ?>>Newest First</option>
                        </select>
                    </div>
                    
                    <!-- Filter Actions -->
                    <div style="margin-top: var(--space-6);">
                        <button type="submit" class="btn btn-primary w-full">
                            <i class="fas fa-filter"></i>
                            Apply Filters
                        </button>
                        <a href="search.php" class="btn btn-secondary w-full" style="margin-top: var(--space-3);">
                            <i class="fas fa-times"></i>
                            Clear All
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="lg:col-span-3">
            <?php if (empty($products)): ?>
                <!-- No Results -->
                <div class="text-center animate-fade-in" style="padding: var(--space-20) 0;">
                    <div style="font-size: var(--font-size-5xl); color: var(--color-gray-400); margin-bottom: var(--space-6);">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 style="color: var(--color-gray-600); margin-bottom: var(--space-4);">No products found</h3>
                    <p style="color: var(--color-gray-500); margin-bottom: var(--space-8);">
                        Try adjusting your search criteria or browse our categories.
                    </p>
                    <div style="display: flex; gap: var(--space-4); justify-content: center; flex-wrap: wrap;">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Browse All Products
                        </a>
                        <a href="search.php" class="btn btn-secondary">
                            <i class="fas fa-redo"></i>
                            Clear Search
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Results Info -->
                <div class="flex justify-between items-center mb-6 animate-fade-in">
                    <div style="color: var(--color-gray-600);">
                        Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_results); ?> 
                        of <?php echo number_format($total_results); ?> results
                    </div>
                    <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);">
                        Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                    </div>
                </div>
                
                <!-- Products Grid -->
                <div class="products-grid stagger-animation">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card hover-lift" 
                             data-category="<?php echo $product['category_id']; ?>" 
                             data-price="<?php echo $product['price']; ?>">
                            <div style="position: relative; overflow: hidden;">
                                <?php echo generateLazyImage(
                                    get_product_image($product['image']), 
                                    htmlspecialchars($product['name']),
                                    'product-card-image',
                                    300,
                                    200
                                ); ?>
                                
                                <!-- Stock Badge -->
                                <?php if ($product['stock'] <= 5 && $product['stock'] > 0): ?>
                                    <div class="badge badge-warning" style="position: absolute; top: var(--space-3); right: var(--space-3);">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Low Stock
                                    </div>
                                <?php elseif ($product['stock'] == 0): ?>
                                    <div class="badge badge-error" style="position: absolute; top: var(--space-3); right: var(--space-3);">
                                        <i class="fas fa-times"></i>
                                        Out of Stock
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-card-content">
                                <!-- Category -->
                                <?php if (!empty($product['category_name'])): ?>
                                    <div style="margin-bottom: var(--space-2);">
                                        <span class="badge badge-primary">
                                            <?php echo htmlspecialchars($product['category_name']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <h3 class="product-card-title">
                                    <a href="product.php?id=<?php echo $product['id']; ?>" class="text-primary">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                    </a>
                                </h3>
                                
                                <p style="color: var(--color-gray-600); font-size: var(--font-size-sm); margin-bottom: var(--space-4);">
                                    <?php echo htmlspecialchars(truncate_text($product['description'], 80)); ?>
                                </p>
                                
                                <div class="flex justify-between items-center">
                                    <div class="product-card-price">
                                        <?php echo format_price($product['price']); ?>
                                    </div>
                                    
                                    <div class="flex gap-2">
                                        <a href="product.php?id=<?php echo $product['id']; ?>" class="btn btn-secondary btn-sm hover-scale">
                                            <i class="fas fa-eye"></i>
                                            View
                                        </a>
                                        <?php if ($product['stock'] > 0 && !is_admin()): ?>
                                            <button class="btn btn-primary btn-sm hover-scale" 
                                                    onclick="quickAddToCart(<?php echo $product['id']; ?>)">
                                                <i class="fas fa-cart-plus"></i>
                                                Add
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination animate-fade-in">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                        
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                               class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                        
                        <div class="pagination-info">
                            Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Quick add to cart function
function quickAddToCart(productId) {
    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}&quantity=1`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product added to cart!', 'success');
            
            // Update cart count
            const cartCount = document.querySelector('.cart-count');
            if (cartCount) {
                cartCount.textContent = data.cart_count;
                cartCount.style.display = 'block';
            }
        } else {
            showNotification(data.message || 'Error adding product to cart', 'error');
        }
    })
    .catch(error => {
        showNotification('Error adding product to cart', 'error');
    });
}

// Enhanced filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterOptions = document.querySelectorAll('.filter-option');
    const form = document.getElementById('filter-form');
    
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            const type = this.dataset.type;
            const value = this.dataset.value;
            
            if (type === 'category') {
                // Remove active from other category options
                filterOptions.forEach(opt => {
                    if (opt.dataset.type === 'category') {
                        opt.classList.remove('active');
                    }
                });
                
                this.classList.add('active');
                
                // Update hidden input or form field
                let categoryInput = form.querySelector('input[name="category"]');
                if (!categoryInput) {
                    categoryInput = document.createElement('input');
                    categoryInput.type = 'hidden';
                    categoryInput.name = 'category';
                    form.appendChild(categoryInput);
                }
                categoryInput.value = value;
                
                // Submit form
                form.submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
