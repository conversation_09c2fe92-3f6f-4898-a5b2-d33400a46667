@echo off
echo ========================================
echo ModernShop Portable Server
echo ========================================
echo.

REM Check if PHP is available
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo.
    echo Please install PHP or use one of these options:
    echo 1. Download XAMPP: https://www.apachefriends.org/download.html
    echo 2. Download PHP: https://windows.php.net/download/
    echo 3. Use the Docker setup: docker-compose up
    echo.
    pause
    exit /b 1
)

echo PHP Version:
php --version
echo.

REM Check required extensions
echo Checking PHP extensions...
php -m | findstr /i "pdo" >nul
if %errorlevel% neq 0 (
    echo ERROR: PDO extension not found
    goto :extensions_help
)

php -m | findstr /i "sqlite" >nul
if %errorlevel% neq 0 (
    echo ERROR: SQLite extension not found
    goto :extensions_help
)

echo ✓ PDO extension found
echo ✓ SQLite extension found

php -m | findstr /i "gd" >nul
if %errorlevel% neq 0 (
    echo ⚠ GD extension not found (image processing will be limited)
) else (
    echo ✓ GD extension found
)

php -m | findstr /i "openssl" >nul
if %errorlevel% neq 0 (
    echo ⚠ OpenSSL extension not found (some security features disabled)
) else (
    echo ✓ OpenSSL extension found
)

echo.
echo Starting PHP development server...
echo Server will be available at: http://localhost:8000
echo.
echo Available pages:
echo - Setup: http://localhost:8000/setup-sqlite.php
echo - Store: http://localhost:8000/
echo - Admin: http://localhost:8000/admin/
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start PHP server
php -S localhost:8000

goto :eof

:extensions_help
echo.
echo SOLUTION: Install missing extensions
echo.
echo Option 1 - Use XAMPP (Recommended):
echo 1. Download from: https://www.apachefriends.org/download.html
echo 2. Install and start Apache
echo 3. Copy project to C:\xampp\htdocs\modernshop
echo 4. Visit: http://localhost/modernshop
echo.
echo Option 2 - Enable in existing PHP:
echo 1. Find php.ini file: php --ini
echo 2. Uncomment these lines (remove ;):
echo    extension=pdo_sqlite
echo    extension=gd
echo    extension=openssl
echo 3. Restart web server
echo.
pause
exit /b 1
