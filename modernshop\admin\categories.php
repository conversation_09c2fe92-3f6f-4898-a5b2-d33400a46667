<?php
$page_title = "Manage Categories";
$page_description = "Add, edit, and manage product categories";
$additional_css = ['../css/admin.css', '../css/forms.css', '../css/animations.css'];
$additional_js = ['../js/forms.js'];

require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

$action = $_GET['action'] ?? 'list';
$category_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verify_csrf_token($csrf_token)) {
        set_flash_message('Invalid request. Please try again.', 'error');
        redirect('categories.php');
    }
    
    $name = sanitize_input($_POST['name'] ?? '');
    
    if (empty($name)) {
        set_flash_message('Category name is required.', 'error');
    } else {
        if ($action === 'add') {
            try {
                $stmt = $pdo->prepare("INSERT INTO categories (name) VALUES (?)");
                $stmt->execute([$name]);
                
                set_flash_message('Category added successfully!', 'success');
                redirect('categories.php');
            } catch (Exception $e) {
                set_flash_message('Error adding category: ' . $e->getMessage(), 'error');
            }
        } elseif ($action === 'edit' && $category_id > 0) {
            try {
                $stmt = $pdo->prepare("UPDATE categories SET name = ? WHERE id = ?");
                $stmt->execute([$name, $category_id]);
                
                set_flash_message('Category updated successfully!', 'success');
                redirect('categories.php');
            } catch (Exception $e) {
                set_flash_message('Error updating category: ' . $e->getMessage(), 'error');
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && $category_id > 0) {
    try {
        // Check if category has products
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $product_count = $stmt->fetch()['count'];
        
        if ($product_count > 0) {
            set_flash_message("Cannot delete category. It has {$product_count} products assigned to it.", 'error');
        } else {
            $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            
            set_flash_message('Category deleted successfully!', 'success');
        }
        redirect('categories.php');
    } catch (Exception $e) {
        set_flash_message('Error deleting category: ' . $e->getMessage(), 'error');
        redirect('categories.php');
    }
}

// Get category for editing
$category = null;
if ($action === 'edit' && $category_id > 0) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
        $stmt->execute([$category_id]);
        $category = $stmt->fetch();
        
        if (!$category) {
            set_flash_message('Category not found.', 'error');
            redirect('categories.php');
        }
    } catch (Exception $e) {
        set_flash_message('Error loading category.', 'error');
        redirect('categories.php');
    }
}

// Get all categories for listing
$categories = [];
if ($action === 'list') {
    try {
        $stmt = $pdo->query("
            SELECT c.*, COUNT(p.id) as product_count 
            FROM categories c 
            LEFT JOIN products p ON c.id = p.category_id 
            GROUP BY c.id 
            ORDER BY c.name
        ");
        $categories = $stmt->fetchAll();
    } catch (Exception $e) {
        set_flash_message('Error loading categories.', 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop Admin</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Admin Header -->
            <div class="admin-header">
                <a href="index.php" class="admin-logo">
                    <i class="fas fa-store"></i>
                    ModernShop Admin
                </a>
                
                <div class="admin-user-info">
                    <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="admin-user-role">Administrator</div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="admin-nav">
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <a href="index.php" class="admin-nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="../index.php" class="admin-nav-item">
                        <i class="fas fa-external-link-alt"></i>
                        View Store
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Catalog</div>
                    <a href="products.php" class="admin-nav-item">
                        <i class="fas fa-box"></i>
                        Products
                    </a>
                    <a href="categories.php" class="admin-nav-item active">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Sales</div>
                    <a href="orders.php" class="admin-nav-item">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                    <a href="customers.php" class="admin-nav-item">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <a href="../logout.php" class="admin-nav-item">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Flash Messages -->
            <?php display_flash_message(); ?>
            
            <?php if ($action === 'list'): ?>
                <!-- Categories List -->
                <div class="admin-page-header animate-fade-in-down">
                    <h1 class="admin-page-title">Categories</h1>
                    <div class="admin-page-actions">
                        <a href="categories.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Add Category
                        </a>
                    </div>
                </div>
                
                <!-- Categories Grid -->
                <?php if (empty($categories)): ?>
                    <div class="data-table-container animate-fade-in-up">
                        <div class="admin-empty">
                            <i class="fas fa-tags"></i>
                            <h3>No categories yet</h3>
                            <p>Start by adding your first product category.</p>
                            <a href="categories.php?action=add" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add First Category
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: var(--space-6);" class="animate-fade-in-up">
                        <?php foreach ($categories as $cat): ?>
                            <div class="card category-card hover-lift" style="padding: var(--space-6);">
                                <div class="flex justify-between items-start mb-4">
                                    <div style="flex: 1;">
                                        <h3 style="margin: 0 0 var(--space-2) 0; color: var(--color-gray-900); font-size: var(--font-size-xl);">
                                            <i class="fas fa-tag" style="color: var(--color-primary); margin-right: var(--space-2);"></i>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                        </h3>
                                        <p style="margin: 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                            <?php echo $cat['product_count']; ?> product<?php echo $cat['product_count'] != 1 ? 's' : ''; ?>
                                        </p>
                                    </div>
                                    
                                    <div class="action-buttons">
                                        <a href="categories.php?action=edit&id=<?php echo $cat['id']; ?>" class="btn btn-primary btn-icon" title="Edit Category">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($cat['product_count'] == 0): ?>
                                            <a href="categories.php?action=delete&id=<?php echo $cat['id']; ?>" class="btn btn-danger btn-icon" title="Delete Category" onclick="return confirm('Are you sure you want to delete this category?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-icon" title="Cannot delete - has products" disabled>
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div style="padding-top: var(--space-4); border-top: 1px solid var(--color-gray-200);">
                                    <div style="display: flex; justify-content: between; align-items: center;">
                                        <span class="status-badge active">
                                            <i class="fas fa-check"></i>
                                            Active
                                        </span>
                                        
                                        <a href="products.php?category=<?php echo $cat['id']; ?>" class="btn btn-outline btn-sm" style="margin-left: auto;">
                                            <i class="fas fa-eye"></i>
                                            View Products
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
            <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Add/Edit Category Form -->
                <div class="admin-page-header animate-fade-in-down">
                    <h1 class="admin-page-title">
                        <?php echo $action === 'add' ? 'Add New Category' : 'Edit Category'; ?>
                    </h1>
                    <div class="admin-page-actions">
                        <a href="categories.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
                
                <div class="admin-form animate-fade-in-up" style="max-width: 600px;">
                    <div class="admin-form-header">
                        <h2 class="admin-form-title">
                            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
                            Category Information
                        </h2>
                    </div>
                    
                    <form method="POST" data-validate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- Category Name -->
                        <div class="form-group">
                            <input type="text" name="name" class="form-input" required 
                                   value="<?php echo htmlspecialchars($category['name'] ?? ''); ?>"
                                   placeholder="Enter category name">
                            <label class="form-label">Category Name</label>
                        </div>
                        
                        <!-- Form Actions -->
                        <div style="margin-top: var(--space-8); display: flex; gap: var(--space-4); justify-content: flex-end;">
                            <a href="categories.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?php echo $action === 'add' ? 'Add Category' : 'Update Category'; ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Category Preview -->
                <?php if ($action === 'edit' && $category): ?>
                    <div class="card animate-fade-in-up" style="margin-top: var(--space-8); max-width: 600px; padding: var(--space-6);">
                        <h3 style="margin-bottom: var(--space-4); color: var(--color-gray-900);">
                            <i class="fas fa-eye"></i>
                            Category Preview
                        </h3>
                        
                        <div class="category-preview" style="padding: var(--space-4); border: 2px dashed var(--color-gray-300); border-radius: var(--radius-md); text-align: center;">
                            <div style="font-size: var(--font-size-2xl); color: var(--color-primary); margin-bottom: var(--space-2);">
                                <i class="fas fa-tag"></i>
                            </div>
                            <h4 style="margin: 0; color: var(--color-gray-900);">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </h4>
                            <p style="margin: var(--space-2) 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                This is how your category will appear to customers
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <?php foreach ($additional_js as $js_file): ?>
        <script src="<?php echo $js_file; ?>"></script>
    <?php endforeach; ?>
    
    <script>
        // Add animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Stagger animation for category cards
            const categoryCards = document.querySelectorAll('.category-card');
            categoryCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.animation = `fadeInUp 0.6s ease-out forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            });
            
            // Enhanced hover effects
            categoryCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                    this.style.boxShadow = 'var(--shadow-2xl)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = 'var(--shadow-sm)';
                });
            });
            
            // Live preview for category name
            const nameInput = document.querySelector('input[name="name"]');
            const preview = document.querySelector('.category-preview h4');
            
            if (nameInput && preview) {
                nameInput.addEventListener('input', function() {
                    const value = this.value.trim();
                    preview.textContent = value || 'Category Name';
                });
            }
        });
    </script>
</body>
</html>
