<?php
$page_title = "Manage Orders";
$page_description = "View and manage customer orders";
$additional_css = ['../css/admin.css', '../css/animations.css'];

require_once '../config/db-sqlite.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

// Get all orders with customer and product information
try {
    $stmt = $pdo->query("
        SELECT o.*, u.username, u.full_name, u.email, p.name as product_name, p.image as product_image
        FROM orders o 
        JOIN users u ON o.user_id = u.id 
        JOIN products p ON o.product_id = p.id 
        ORDER BY o.order_date DESC
    ");
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    $orders = [];
    set_flash_message('Error loading orders.', 'error');
}

// Group orders by order date and user (assuming same-day orders from same user are one order)
$grouped_orders = [];
foreach ($orders as $order) {
    $key = $order['user_id'] . '_' . date('Y-m-d', strtotime($order['order_date']));
    if (!isset($grouped_orders[$key])) {
        $grouped_orders[$key] = [
            'order_info' => $order,
            'items' => [],
            'total' => 0
        ];
    }
    $grouped_orders[$key]['items'][] = $order;
    $grouped_orders[$key]['total'] += $order['total_price'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop Admin</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Admin Header -->
            <div class="admin-header">
                <a href="index.php" class="admin-logo">
                    <i class="fas fa-store"></i>
                    ModernShop Admin
                </a>
                
                <div class="admin-user-info">
                    <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="admin-user-role">Administrator</div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="admin-nav">
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <a href="index.php" class="admin-nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="../index.php" class="admin-nav-item">
                        <i class="fas fa-external-link-alt"></i>
                        View Store
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Catalog</div>
                    <a href="products.php" class="admin-nav-item">
                        <i class="fas fa-box"></i>
                        Products
                    </a>
                    <a href="categories.php" class="admin-nav-item">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Sales</div>
                    <a href="orders.php" class="admin-nav-item active">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                    <a href="customers.php" class="admin-nav-item">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <a href="../logout.php" class="admin-nav-item">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Flash Messages -->
            <?php display_flash_message(); ?>
            
            <!-- Page Header -->
            <div class="admin-page-header animate-fade-in-down">
                <h1 class="admin-page-title">Orders</h1>
                <div class="admin-page-actions">
                    <button class="btn btn-secondary" onclick="exportOrders()">
                        <i class="fas fa-download"></i>
                        Export Orders
                    </button>
                </div>
            </div>
            
            <!-- Orders Statistics -->
            <div class="dashboard-grid animate-fade-in-up" style="margin-bottom: var(--space-8);">
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Orders</div>
                        <div class="dashboard-card-icon primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo count($grouped_orders); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +8% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Revenue</div>
                        <div class="dashboard-card-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value">
                        <?php 
                        $total_revenue = array_sum(array_column($grouped_orders, 'total'));
                        echo format_price($total_revenue); 
                        ?>
                    </div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +15% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Average Order</div>
                        <div class="dashboard-card-icon warning">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value">
                        <?php 
                        $avg_order = count($grouped_orders) > 0 ? $total_revenue / count($grouped_orders) : 0;
                        echo format_price($avg_order); 
                        ?>
                    </div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +5% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Items Sold</div>
                        <div class="dashboard-card-icon error">
                            <i class="fas fa-box"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo count($orders); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +12% from last month
                    </div>
                </div>
            </div>
            
            <!-- Orders List -->
            <?php if (empty($grouped_orders)): ?>
                <div class="data-table-container animate-fade-in-up">
                    <div class="admin-empty">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>No orders yet</h3>
                        <p>Orders will appear here when customers make purchases.</p>
                        <a href="../index.php" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i>
                            View Store
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="orders-list animate-fade-in-up">
                    <?php foreach ($grouped_orders as $key => $group): ?>
                        <?php 
                        $order_info = $group['order_info'];
                        $items = $group['items'];
                        $total = $group['total'];
                        $order_number = str_pad($order_info['id'], 6, '0', STR_PAD_LEFT);
                        ?>
                        
                        <div class="card order-card hover-lift" style="margin-bottom: var(--space-6);">
                            <!-- Order Header -->
                            <div class="card-header" style="background: var(--color-gray-50);">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h3 style="margin: 0; color: var(--color-gray-900);">
                                            Order #<?php echo $order_number; ?>
                                        </h3>
                                        <p style="margin: var(--space-1) 0 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                            <i class="fas fa-user"></i>
                                            <?php echo htmlspecialchars($order_info['full_name']); ?> 
                                            (<?php echo htmlspecialchars($order_info['username']); ?>)
                                        </p>
                                        <p style="margin: var(--space-1) 0 0 0; color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                            <i class="fas fa-envelope"></i>
                                            <?php echo htmlspecialchars($order_info['email']); ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <div style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                                            <?php echo format_price($total); ?>
                                        </div>
                                        <div style="margin-top: var(--space-1);">
                                            <span class="status-badge active">
                                                <i class="fas fa-check"></i>
                                                Completed
                                            </span>
                                        </div>
                                        <div style="margin-top: var(--space-1); font-size: var(--font-size-sm); color: var(--color-gray-600);">
                                            <?php echo date('M j, Y g:i A', strtotime($order_info['order_date'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Order Items -->
                            <div class="card-body">
                                <h4 style="margin-bottom: var(--space-4); color: var(--color-gray-900);">
                                    <i class="fas fa-list"></i>
                                    Order Items (<?php echo count($items); ?>)
                                </h4>
                                
                                <div style="display: grid; gap: var(--space-3);">
                                    <?php foreach ($items as $item): ?>
                                        <div class="order-item-row" style="display: flex; gap: var(--space-4); align-items: center; padding: var(--space-3); border: 1px solid var(--color-gray-200); border-radius: var(--radius-md);">
                                            <!-- Product Image -->
                                            <div class="flex-shrink-0">
                                                <img 
                                                    src="<?php echo get_product_image($item['product_image']); ?>" 
                                                    alt="<?php echo htmlspecialchars($item['product_name']); ?>"
                                                    style="width: 60px; height: 60px; object-fit: cover; border-radius: var(--radius-md);"
                                                >
                                            </div>
                                            
                                            <!-- Product Details -->
                                            <div class="flex-1">
                                                <h5 style="font-size: var(--font-size-base); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-1); color: var(--color-gray-900);">
                                                    <?php echo htmlspecialchars($item['product_name']); ?>
                                                </h5>
                                                <div style="display: flex; gap: var(--space-4); color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                                    <span>
                                                        <i class="fas fa-cube"></i>
                                                        Qty: <?php echo $item['quantity']; ?>
                                                    </span>
                                                    <span>
                                                        <i class="fas fa-dollar-sign"></i>
                                                        <?php echo format_price($item['total_price'] / $item['quantity']); ?> each
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <!-- Item Total -->
                                            <div class="text-right">
                                                <div style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                                                    <?php echo format_price($item['total_price']); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <!-- Order Footer -->
                            <div class="card-footer" style="background: var(--color-gray-50);">
                                <div class="flex justify-between items-center">
                                    <div style="color: var(--color-gray-600); font-size: var(--font-size-sm);">
                                        <i class="fas fa-truck"></i>
                                        Estimated delivery: <?php echo date('M j, Y', strtotime($order_info['order_date'] . ' +5 days')); ?>
                                    </div>
                                    <div class="flex gap-3">
                                        <button class="btn btn-secondary btn-sm" onclick="viewOrderDetails('<?php echo $order_number; ?>')">
                                            <i class="fas fa-eye"></i>
                                            View Details
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="updateOrderStatus('<?php echo $order_info['id']; ?>')">
                                            <i class="fas fa-edit"></i>
                                            Update Status
                                        </button>
                                        <button class="btn btn-outline btn-sm" onclick="printOrder('<?php echo $order_number; ?>')">
                                            <i class="fas fa-print"></i>
                                            Print
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    
    <script>
        // Order management functions
        function viewOrderDetails(orderNumber) {
            showNotification(`Viewing details for order ${orderNumber}`, 'info');
        }
        
        function updateOrderStatus(orderId) {
            showNotification('Order status update feature coming soon', 'info');
        }
        
        function printOrder(orderNumber) {
            showNotification(`Printing order ${orderNumber}`, 'info');
        }
        
        function exportOrders() {
            showNotification('Export feature coming soon', 'info');
        }
        
        // Add animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Stagger animation for order cards
            const orderCards = document.querySelectorAll('.order-card');
            orderCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.animation = `fadeInUp 0.6s ease-out forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            });
            
            // Enhanced hover effects for order items
            const orderItems = document.querySelectorAll('.order-item-row');
            orderItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.borderColor = 'var(--color-primary)';
                    this.style.backgroundColor = 'var(--color-primary-50)';
                    this.style.transform = 'scale(1.01)';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.borderColor = 'var(--color-gray-200)';
                    this.style.backgroundColor = 'transparent';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // Dashboard cards animation
            const dashboardCards = document.querySelectorAll('.dashboard-card');
            dashboardCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.animation = `fadeInUp 0.6s ease-out forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
