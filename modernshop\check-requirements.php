<?php
/**
 * PHP Requirements Checker for ModernShop
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ModernShop - System Requirements</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 20px; }
        h1 { color: #1e293b; margin-bottom: 20px; }
        .requirement { display: flex; align-items: center; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .requirement.pass { background: #dcfce7; border-left: 4px solid #16a34a; }
        .requirement.fail { background: #fef2f2; border-left: 4px solid #dc2626; }
        .requirement.warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
        .icon { margin-right: 15px; font-size: 20px; }
        .pass .icon { color: #16a34a; }
        .fail .icon { color: #dc2626; }
        .warning .icon { color: #f59e0b; }
        .details { margin-left: 35px; font-size: 14px; color: #6b7280; }
        .btn { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #2563eb; }
        .btn.success { background: #16a34a; }
        .btn.success:hover { background: #15803d; }
        .code-block { background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; margin: 15px 0; overflow-x: auto; }
        .code-block pre { margin: 0; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #374151; margin-bottom: 15px; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; }
        .os-detection { background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px; padding: 15px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 ModernShop System Requirements</h1>
            
            <?php
            // Detect operating system
            $os = PHP_OS_FAMILY;
            $is_windows = $os === 'Windows';
            $is_linux = $os === 'Linux';
            $is_mac = $os === 'Darwin';
            
            echo "<div class='os-detection'>";
            echo "<strong>Detected OS:</strong> " . PHP_OS . " (" . $os . ")";
            echo "</div>";
            
            // Check PHP version
            $php_version = PHP_VERSION;
            $php_required = '7.4.0';
            $php_ok = version_compare($php_version, $php_required, '>=');
            ?>
            
            <div class="section">
                <h2>PHP Requirements</h2>
                
                <div class="requirement <?php echo $php_ok ? 'pass' : 'fail'; ?>">
                    <span class="icon"><?php echo $php_ok ? '✅' : '❌'; ?></span>
                    <div>
                        <strong>PHP Version:</strong> <?php echo $php_version; ?> 
                        <?php echo $php_ok ? '(OK)' : '(Requires 7.4+)'; ?>
                    </div>
                </div>
                
                <?php
                $required_extensions = [
                    'pdo' => 'PDO (PHP Data Objects)',
                    'pdo_mysql' => 'PDO MySQL Driver',
                    'gd' => 'GD Library (Image Processing)',
                    'openssl' => 'OpenSSL (Encryption)',
                    'json' => 'JSON Support',
                    'mbstring' => 'Multibyte String',
                    'curl' => 'cURL (HTTP Requests)'
                ];
                
                $missing_extensions = [];
                $all_extensions_ok = true;
                
                foreach ($required_extensions as $ext => $description) {
                    $loaded = extension_loaded($ext);
                    if (!$loaded) {
                        $missing_extensions[] = $ext;
                        $all_extensions_ok = false;
                    }
                    
                    echo "<div class='requirement " . ($loaded ? 'pass' : 'fail') . "'>";
                    echo "<span class='icon'>" . ($loaded ? '✅' : '❌') . "</span>";
                    echo "<div><strong>$description:</strong> " . ($loaded ? 'Installed' : 'Missing') . "</div>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <?php if (!empty($missing_extensions)): ?>
            <div class="section">
                <h2>🚨 Missing Extensions</h2>
                <p>The following PHP extensions are required but not installed:</p>
                <ul style="margin: 15px 0 15px 30px;">
                    <?php foreach ($missing_extensions as $ext): ?>
                        <li><code><?php echo $ext; ?></code></li>
                    <?php endforeach; ?>
                </ul>
                
                <h3>Installation Instructions:</h3>
                
                <?php if ($is_windows): ?>
                <div class="code-block">
                    <strong>For Windows (XAMPP/WAMP):</strong>
                    <pre>1. Open php.ini file (usually in C:\xampp\php\php.ini)
2. Find and uncomment these lines (remove the ; at the beginning):
<?php foreach ($missing_extensions as $ext): ?>
   extension=<?php echo $ext; ?>
<?php endforeach; ?>

3. Save the file and restart Apache</pre>
                </div>
                
                <div class="code-block">
                    <strong>For Windows (Standalone PHP):</strong>
                    <pre>1. Download PHP extensions from https://windows.php.net/downloads/pecl/
2. Copy .dll files to your PHP ext/ directory
3. Add extension lines to php.ini
4. Restart web server</pre>
                </div>
                <?php endif; ?>
                
                <?php if ($is_linux): ?>
                <div class="code-block">
                    <strong>For Ubuntu/Debian:</strong>
                    <pre>sudo apt update
sudo apt install <?php echo implode(' ', array_map(function($ext) { 
    return 'php-' . str_replace('pdo_', '', $ext); 
}, $missing_extensions)); ?>

# Restart web server
sudo systemctl restart apache2</pre>
                </div>
                
                <div class="code-block">
                    <strong>For CentOS/RHEL:</strong>
                    <pre>sudo yum install <?php echo implode(' ', array_map(function($ext) { 
    return 'php-' . str_replace('pdo_', '', $ext); 
}, $missing_extensions)); ?>

# Restart web server
sudo systemctl restart httpd</pre>
                </div>
                <?php endif; ?>
                
                <?php if ($is_mac): ?>
                <div class="code-block">
                    <strong>For macOS (Homebrew):</strong>
                    <pre>brew install php
# Extensions are usually included with Homebrew PHP

# Or install specific extensions:
brew install php@8.1</pre>
                </div>
                <?php endif; ?>
                
                <div class="code-block">
                    <strong>Using Docker (Recommended):</strong>
                    <pre># Run with Docker (all extensions included)
docker-compose up -d

# Or build custom image
docker build -t modernshop .
docker run -p 8000:80 modernshop</pre>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="section">
                <h2>📁 Directory Permissions</h2>
                <?php
                $directories = ['uploads', 'cache', 'logs'];
                foreach ($directories as $dir) {
                    $exists = is_dir($dir);
                    $writable = $exists && is_writable($dir);
                    
                    echo "<div class='requirement " . ($writable ? 'pass' : ($exists ? 'warning' : 'fail')) . "'>";
                    echo "<span class='icon'>" . ($writable ? '✅' : ($exists ? '⚠️' : '❌')) . "</span>";
                    echo "<div><strong>$dir/ directory:</strong> ";
                    
                    if (!$exists) {
                        echo "Does not exist";
                    } elseif (!$writable) {
                        echo "Exists but not writable";
                    } else {
                        echo "OK";
                    }
                    
                    echo "</div></div>";
                }
                ?>
                
                <?php if (!is_dir('uploads') || !is_dir('cache') || !is_dir('logs')): ?>
                <div class="code-block">
                    <strong>Create missing directories:</strong>
                    <pre>mkdir -p uploads cache logs
chmod 755 uploads cache logs</pre>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="section">
                <h2>🎯 Next Steps</h2>
                
                <?php if ($all_extensions_ok && $php_ok): ?>
                    <div class="requirement pass">
                        <span class="icon">🎉</span>
                        <div><strong>All requirements met!</strong> You can proceed with the installation.</div>
                    </div>
                    
                    <a href="setup.php" class="btn success">Continue to Setup</a>
                    <a href="index.php" class="btn">View Store</a>
                    
                <?php else: ?>
                    <div class="requirement fail">
                        <span class="icon">⚠️</span>
                        <div><strong>Requirements not met.</strong> Please install missing extensions first.</div>
                    </div>
                    
                    <button onclick="location.reload()" class="btn">Recheck Requirements</button>
                <?php endif; ?>
            </div>
            
            <div class="section">
                <h2>📋 System Information</h2>
                <div class="code-block">
                    <pre>PHP Version: <?php echo PHP_VERSION; ?>
Server Software: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
Operating System: <?php echo PHP_OS; ?>
PHP SAPI: <?php echo PHP_SAPI; ?>
Memory Limit: <?php echo ini_get('memory_limit'); ?>
Max Execution Time: <?php echo ini_get('max_execution_time'); ?>s
Upload Max Filesize: <?php echo ini_get('upload_max_filesize'); ?>
Post Max Size: <?php echo ini_get('post_max_size'); ?></pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
