<?php
require_once 'config/db.php';
require_once 'includes/functions.php';

$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($product_id <= 0) {
    set_flash_message('Product not found.', 'error');
    redirect('index.php');
}

// Get product details
try {
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = ?
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        set_flash_message('Product not found.', 'error');
        redirect('index.php');
    }
} catch (Exception $e) {
    set_flash_message('Error loading product details.', 'error');
    redirect('index.php');
}

// Get related products from same category
try {
    $related_stmt = $pdo->prepare("
        SELECT * FROM products 
        WHERE category_id = ? AND id != ? 
        ORDER BY RAND() 
        LIMIT 4
    ");
    $related_stmt->execute([$product['category_id'], $product_id]);
    $related_products = $related_stmt->fetchAll();
} catch (Exception $e) {
    $related_products = [];
}

$page_title = htmlspecialchars($product['name']);
$page_description = htmlspecialchars(truncate_text($product['description'], 150));

include 'includes/header.php';
?>

<div class="container animate-fade-in-up">
    <!-- Breadcrumb -->
    <nav style="margin: var(--space-6) 0; color: var(--color-gray-600);">
        <a href="index.php" class="text-primary">Home</a>
        <span style="margin: 0 var(--space-2);">/</span>
        <?php if (!empty($product['category_name'])): ?>
            <a href="index.php?category=<?php echo $product['category_id']; ?>" class="text-primary">
                <?php echo htmlspecialchars($product['category_name']); ?>
            </a>
            <span style="margin: 0 var(--space-2);">/</span>
        <?php endif; ?>
        <span><?php echo htmlspecialchars($product['name']); ?></span>
    </nav>
    
    <!-- Product Details -->
    <div class="grid lg:grid-cols-2 gap-12 mb-16">
        <!-- Product Image -->
        <div class="animate-fade-in-left">
            <div class="product-image-container" style="position: relative; border-radius: var(--radius-xl); overflow: hidden; box-shadow: var(--shadow-lg);">
                <img 
                    src="<?php echo get_product_image($product['image']); ?>" 
                    alt="<?php echo htmlspecialchars($product['name']); ?>"
                    style="width: 100%; height: 500px; object-fit: cover; transition: transform var(--transition-slow);"
                    onmouseover="this.style.transform='scale(1.05)'"
                    onmouseout="this.style.transform='scale(1)'"
                >
                
                <!-- Stock Badge -->
                <?php if ($product['stock'] <= 5 && $product['stock'] > 0): ?>
                    <div class="badge badge-warning" style="position: absolute; top: var(--space-4); right: var(--space-4);">
                        <i class="fas fa-exclamation-triangle"></i>
                        Low Stock
                    </div>
                <?php elseif ($product['stock'] == 0): ?>
                    <div class="badge badge-error" style="position: absolute; top: var(--space-4); right: var(--space-4);">
                        <i class="fas fa-times"></i>
                        Out of Stock
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Product Info -->
        <div class="animate-fade-in-right">
            <div style="position: sticky; top: var(--space-20);">
                <!-- Category -->
                <?php if (!empty($product['category_name'])): ?>
                    <div style="margin-bottom: var(--space-4);">
                        <span class="badge badge-primary">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </span>
                    </div>
                <?php endif; ?>
                
                <!-- Product Name -->
                <h1 style="font-size: var(--font-size-4xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin-bottom: var(--space-4);">
                    <?php echo htmlspecialchars($product['name']); ?>
                </h1>
                
                <!-- Price -->
                <div style="margin-bottom: var(--space-6);">
                    <span style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                        <?php echo format_price($product['price']); ?>
                    </span>
                </div>
                
                <!-- Stock Info -->
                <div style="margin-bottom: var(--space-6); padding: var(--space-4); background: var(--color-gray-50); border-radius: var(--radius-md);">
                    <?php if ($product['stock'] > 0): ?>
                        <div style="color: var(--color-success); font-weight: var(--font-weight-medium);">
                            <i class="fas fa-check-circle"></i>
                            In Stock (<?php echo $product['stock']; ?> available)
                        </div>
                        <?php if ($product['stock'] <= 10): ?>
                            <div style="color: var(--color-warning); font-size: var(--font-size-sm); margin-top: var(--space-1);">
                                <i class="fas fa-clock"></i>
                                Only <?php echo $product['stock']; ?> left - order soon!
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div style="color: var(--color-error); font-weight: var(--font-weight-medium);">
                            <i class="fas fa-times-circle"></i>
                            Out of Stock
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Description -->
                <div style="margin-bottom: var(--space-8);">
                    <h3 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-3);">
                        Description
                    </h3>
                    <p style="color: var(--color-gray-700); line-height: var(--line-height-relaxed);">
                        <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                    </p>
                </div>
                
                <!-- Add to Cart Section -->
                <?php if (!is_admin()): ?>
                    <div style="border-top: 1px solid var(--color-gray-200); padding-top: var(--space-6);">
                        <?php if ($product['stock'] > 0): ?>
                            <form id="add-to-cart-form" style="display: flex; gap: var(--space-4); align-items: center; margin-bottom: var(--space-4);">
                                <!-- Quantity Selector -->
                                <div style="display: flex; align-items: center; border: 2px solid var(--color-gray-300); border-radius: var(--radius-md); overflow: hidden;">
                                    <button type="button" class="quantity-btn" data-action="decrease" 
                                            style="padding: var(--space-3); background: var(--color-gray-100); border: none; cursor: pointer; transition: background var(--transition-fast);"
                                            onmouseover="this.style.background='var(--color-gray-200)'"
                                            onmouseout="this.style.background='var(--color-gray-100)'">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="quantity-input" value="1" min="1" max="<?php echo $product['stock']; ?>" 
                                           style="width: 60px; text-align: center; border: none; padding: var(--space-3); font-weight: var(--font-weight-medium);">
                                    <button type="button" class="quantity-btn" data-action="increase" 
                                            style="padding: var(--space-3); background: var(--color-gray-100); border: none; cursor: pointer; transition: background var(--transition-fast);"
                                            onmouseover="this.style.background='var(--color-gray-200)'"
                                            onmouseout="this.style.background='var(--color-gray-100)'">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                
                                <!-- Add to Cart Button -->
                                <button type="submit" class="btn btn-primary btn-lg flex-1 hover-lift">
                                    <i class="fas fa-cart-plus"></i>
                                    Add to Cart
                                </button>
                            </form>
                            
                            <!-- Quick Actions -->
                            <div style="display: flex; gap: var(--space-3);">
                                <button class="btn btn-secondary flex-1 hover-lift">
                                    <i class="fas fa-heart"></i>
                                    Add to Wishlist
                                </button>
                                <button class="btn btn-secondary hover-lift">
                                    <i class="fas fa-share-alt"></i>
                                    Share
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                This product is currently out of stock. Please check back later.
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Product Features -->
                <div style="margin-top: var(--space-8); padding: var(--space-6); background: var(--color-primary-50); border-radius: var(--radius-lg);">
                    <h4 style="font-weight: var(--font-weight-semibold); margin-bottom: var(--space-4); color: var(--color-primary-700);">
                        Why Choose This Product?
                    </h4>
                    <div style="display: grid; gap: var(--space-3);">
                        <div style="display: flex; align-items: center; gap: var(--space-3);">
                            <i class="fas fa-shipping-fast" style="color: var(--color-primary);"></i>
                            <span>Fast & Free Shipping</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: var(--space-3);">
                            <i class="fas fa-undo" style="color: var(--color-primary);"></i>
                            <span>30-Day Return Policy</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: var(--space-3);">
                            <i class="fas fa-shield-alt" style="color: var(--color-primary);"></i>
                            <span>1-Year Warranty</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: var(--space-3);">
                            <i class="fas fa-headset" style="color: var(--color-primary);"></i>
                            <span>24/7 Customer Support</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products -->
    <?php if (!empty($related_products)): ?>
        <section style="margin-top: var(--space-20);">
            <div class="section-header">
                <h2 class="section-title">Related Products</h2>
                <p class="section-subtitle">You might also like these products</p>
            </div>
            
            <div class="products-grid stagger-animation">
                <?php foreach ($related_products as $related): ?>
                    <div class="product-card hover-lift">
                        <div style="position: relative; overflow: hidden;">
                            <img 
                                src="<?php echo get_product_image($related['image']); ?>" 
                                alt="<?php echo htmlspecialchars($related['name']); ?>"
                                class="product-card-image"
                            >
                        </div>
                        
                        <div class="product-card-content">
                            <h3 class="product-card-title">
                                <?php echo htmlspecialchars($related['name']); ?>
                            </h3>
                            
                            <p style="color: var(--color-gray-600); font-size: var(--font-size-sm); margin-bottom: var(--space-3);">
                                <?php echo htmlspecialchars(truncate_text($related['description'], 60)); ?>
                            </p>
                            
                            <div class="flex justify-between items-center">
                                <div class="product-card-price">
                                    <?php echo format_price($related['price']); ?>
                                </div>
                                
                                <a href="product.php?id=<?php echo $related['id']; ?>" class="btn btn-primary btn-sm hover-scale">
                                    <i class="fas fa-eye"></i>
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    const quantityInput = document.querySelector('.quantity-input');
    const decreaseBtn = document.querySelector('[data-action="decrease"]');
    const increaseBtn = document.querySelector('[data-action="increase"]');
    const maxStock = <?php echo $product['stock']; ?>;
    
    if (decreaseBtn && increaseBtn && quantityInput) {
        decreaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });
        
        increaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value < maxStock) {
                quantityInput.value = value + 1;
            }
        });
        
        quantityInput.addEventListener('change', function() {
            let value = parseInt(this.value);
            if (value < 1) this.value = 1;
            if (value > maxStock) this.value = maxStock;
        });
    }
    
    // Add to cart form
    const addToCartForm = document.getElementById('add-to-cart-form');
    if (addToCartForm) {
        addToCartForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const quantity = quantityInput.value;
            const productId = <?php echo $product_id; ?>;
            
            // Add loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="loading-dots"><span></span><span></span><span></span></span> Adding...';
            submitBtn.disabled = true;
            
            // Simulate add to cart (replace with actual AJAX call)
            setTimeout(() => {
                // Add to session cart
                fetch('cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=add&product_id=${productId}&quantity=${quantity}`
                })
                .then(response => response.text())
                .then(data => {
                    showNotification('Product added to cart successfully!', 'success');
                    
                    // Update cart count if element exists
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        const currentCount = parseInt(cartCount.textContent) || 0;
                        cartCount.textContent = currentCount + parseInt(quantity);
                        cartCount.style.display = 'block';
                    }
                })
                .catch(error => {
                    showNotification('Error adding product to cart', 'error');
                })
                .finally(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            }, 1000);
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
