<!doctype html>
<html lang="ro">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Windows</title>

    <meta name="description" content="Instructions on how to install XAMPP for Windows distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="ro ro_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/ro/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/ro/faq.html">Întrebări frecvente - FAQs</a></li>
              <li class="item "><a href="/dashboard/ro/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Windows <span>Întrebări Frecvente - FAQ</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
      <dt>Cum instalez XAMPP?</dt>
      <dd>
      <p>Există trei variante de XAMPP pentru Windows:</p>
      <p>Installer:<br />
      Probabil cea mai uşoară cale de a instala XAMPP.</p>
      <p>ZIP:<br />
      XAMPP în varianta ZIP obişnuită.</p>
      <p>7zip:<br />
      În situaţia unei viteze mici de descărcare: XAMPP ca arhivă 7zip.</p>
      <p>Notă: Dacă extrageţi fişierele, puteţi primi un avertisment pozitiv-fals de la antivirus.</p>
      <p><strong>Folosind programul de instalare:</strong></p>
      <p></p>
      <p>Panoul de control XAMPP pentru pornirea/oprirea Apache, MySQL, FileZilla & Mercury, sau de instalare a acestor servere drept servicii.</p>
      <p><strong>Instalare din arhivă ZIP</strong></p>
      <p>Dezarhivează arhivele ZIP într-un folder la alegerea ta. XAMPP este extras în subdirectorul "C:\\xampp" sub directorul ţintă selectat. Acum startaţi fişierul "setup_xampp.bat", pentru a potrivi configuraţia XAMPP la sistemul vostru.</p>
      <p>Dacă alegeţi "C:\\" drept ţintă a directorului root , nu trebuie să porniţi "setup_xampp.bat".</p>
      <p>Ca şi cu versiunea de instalare, puteţi folosi acum "Panoul de XAMPP de control" pentru sarcini suplimentare.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
      <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Cum pot porni XAMPP fără instalare?</dt>
      <dd>
      <p>Dacă extrageţi XAMPP într-un director principal precum "C:\\" sau "D:\\", puteţi porni majoritarea serverelor, cum ar fi Apache sau MySQL, fără executarea fişierului "setup_xampp.bat".</p>
      <p>Nefolosind scriptul de configurare, sau selectând căi relative în script-ul de configurare, este de preferat pentru istalarea XAMPP pe un drive USB. Pentru că pe fiecare PC o astfel de unitate poate avea o altă literă de alocată. Puteți trece de la absolut la relativ în orice moment, cu script-ul de configurare.</p>
      <p>Folosind programul de instalare de pe pagina noastră Download-uri este cel mai simplu mod de a instala XAMPP. După ce instalarea este completă, veţi găsi XAMPP în   Start | Programe | XAMPP. Puteţi utiliza panoul de control XAMPP pentru a porni/opri toate serverele şi de asemenea să instalaţi/dezinstalaţi servicii.</p>
      <p>Panoul de control XAMPP pentru pornirea/oprirea Apache, MySQL, FileZilla & Mercury, sau de instalare a acestor servere drept servicii.</p>
      </dd>
      <dt>Cum pornesc şi opresc XAMPP?</dt>
      <dd>
      <p>Centrul universal de control este "Control Panel XAMPP" (mulţumiri  www.nat32.com). Se începe cu:</p>
      <p><code>\xampp\xampp-control.exe</code></p>
      <p>Puteţi folosi, de asemenea, unele fişiere stive de comenzi - batch pentru a porni/opri serverele:</p>
      <p>
      <ul>
        <li>Apache &amp; MySQL start:
        <code>\xampp\xampp_start.exe</code></li>
        <li>Apache &amp; MySQL stop:
        <code>\xampp\xampp_stop.exe</code></li>
        <li>Apache start:
        <code>\xampp\apache_start.bat</code></li>
        <li>Apache stop:
        <code>\xampp\apache_stop.bat</code></li>
        <li>MySQL start:
        <code>\xampp\mysql_start.bat</code></li>
        <li>MySQL stop:
        <code>\xampp\mysql_stop.bat</code></li>
        <li>Mercury Mailserver start:
        <code>\xampp\mercury_start.bat</code></li>
        <li>Mercury Mailserver stop:
        <code>\xampp\mercury_stop.bat</code></li>
        <li>FileZilla Server start:
        <code>\xampp\filezilla_start.bat</code></li>
        <li>FileZilla Server stop:
        <code>\xampp\filezilla_stop.bat</code></li></ul></p>
      </p>
      </dd>
      <dt>Com poţi testa dacă totul funcţionază?</dt>
      <dd>
      <p>Tastează următoarea adresă URL în browser-ul tău favorit:</p>
      <p><code>http://localhost/</code> sau  <code>http://127.0.0.1/</code> </p>
      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-windows-start.jpg" />
      </dd>
      <dt>Cum pot instala serverul drept serviciu?</dt>
      <dd>
      <p>Puteţi instala de asemenea fiecare server din XAMPP drept serviciu Windows. Îl puteţi instala dealtfel din Panoul de Control XAMPP. În această situaţie, este necesar a rula scripturile sau Panoul de Control cu drepturi de Administrator.</p>
      <p>Instalare serviciu Apache: \\xampp\\apache\\apache_installservice.bat</p>
      <p>Dezinstalare serviciu Apache: \\xampp\\apache\\apache_uninstallservice.bat </p>
      <p>Instalare serviciu MySQL: \\xampp\\mysql\\mysql_installservice.bat </p>
      <p>Dezinstalare serviciu MySQL: \\xampp\\mysql\\mysql_uninstallservice.bat </p>
      <p>(Dez)Instalare serviciu FileZilla: \\xampp\\filezilla_setup.bat </p>
      <p>Mercury: Nici o instalare de serviciu disponibilă</p>
      </dd>
      <dt>Poate fi folosit în producţie XAMPP?</dt>
      <dd>
      <p>XAMPP nu este adecvat utilizării în producţie, ci doar în medii de dezvoltare. El este configurat în modul cel mai deschis, pentru ai permite programatorului tot ce acesta îşi doreşte. Aceasta convine perfect în medii de dezvoltare, dar poate fi un dezastru într-un mediu de producţie.</p>
      <p>Iată o listă de vulnerabilităţi ale XAMPP:</p>
      <ol>
        <li>Administratorul MySQL (contul de root) nu are parolă.</li>
        <li>Se poate accesa în reţea daemon-ul MySQL.</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
        <li>Utilizatorii impliciţi ai Mercury si FileZilla sunt cunoscuţi.</li>
      </ol>
      <p>Toate punctele pot reprezenta un imens risc de securitate. Mai ales dacă XAMPP este accesibil prin intermediul reţelei şi persoanelor din afara LAN-ului vostru. De ajutor poate fi de asemenea utilizarea unui firewall sau a unui router (NAT) . Folosind un router sau un firewall, PC-ul vostru este în mod normal inaccesibil în reţea. Rezolvarea acestor probleme, vă revine. De un oarecare ajutor este "Consola de securitate XAMPP".</p>
      <p>Vă rugăm să protejaţi XAMPP înainte de a publica on-line. Un firewall sau un router extern sunt suficiente doar pentru un nivel scăzut de securitate. Pentru mai multă siguranţă, puteți rula "consola XAMPP Security" unde puteţi atribui parole.</p>
      <p>Dacă doriţi să aveţi XAMPP accesibil pe internet, trebuie să mergeţi la următoarea adresă URI care poate rezolva unele probleme:</p>
      <p><code> http://localhost/security/</code></p>
      <p>Cu consola de securitate puteţi seta o parolă pentru utilizator "root" MySQL şi phpMyAdmin. Puteţi activa, de asemenea, o autentificare pentru demopages XAMPP.</p>
      <p>Acest instrument bazat pe web nu rezolvă probleme suplimentare de securitate! Mai ales serverul FileZilla FTP şi serverul de mail Mercury securizate de d-voastră.</p></dd>
      <dt>Cum instalez XAMPP?</dt>
      <dd>
      <p>Dacă aţi instalat XAMPP folosind versiunea de instalare, vă rugăm să utilizaţi programul de dezinstalare. Dezinstalarea va şterge toate intrările XAMPP în regiştrii şi va dezinstala unele servicii incluse cu XAMPP. Vă recomandăm să utilizaţi programul de dezinstalare pentru îndepărtarea instalărilor XAMPP din versiunea kitului de instalare. Vă rugăm să faceţi copii de siguraţă a datelor pe care doriţi să le păstraţi, înainte de dezinstalarea XAMPP.</p>
      <p>Dacă aţi instalat XAMPP folosind versiuni ale ZIP şi 7zip, închideţi toate serverele XAMPP şi ieşiţi din toate panourile. Dacă aţi instalat oricare servicii, opriţi-le şi de asemenea dezinstalaţi-le. Apoi, ştergeţi simplu întregul folder în care s-a instalat XAMPP. Nu există intrări în Registry sau variabile de mediu care să  necesite curăţire.</p>
      </dd>
      <dt>Ce este versiunea "redusă" a XAMPP?</dt>
      <dd>
      <p>XAMPP Lite (însemnând "redus" ca în "greutate redusă") este un pachet mai mic de componente XAMPP, recomandat pentru treburi urgente, folosind doar PHP şi MySQL. Unele servere sau instrumente, cum ar fi Mercury Mail şi FileZilla FTP lipsesc din versiunea Lite.</p>
      </dd>
      <dt>Unde ar trebui să plasez conţinutul web-ului meu?</dt>
      <dd>
      <p>Directorul principal pentru toate documentele WWW este \\xampp\\htdocs. Dacă în acest director puneţi un fişier "test.html" , îl puteţi accesa cu URI-ul "http://localhost/test.html".</p>
      <p>Şi în cazul "test.php"? Folosiţi simplu "http://localhost/test.php". Un script de test simplu poate fi:</p>
      <p><code>&lt;?php<br />
        echo 'Hello world'; <br />
        ?&gt;</code></p>
      <p>Un subdirector nou pentru web-ul vostru? Trebuie doar să creeaţi un nou director (de ex. "new") în directorul "\\xampp\\htdocs" (cel mai bine fără spaţii albe şi doar ASCII), în acest director creeaţi un fişier de test şi accesaţi-l cu "http://localhost/new/test.php".</p>
      <p><strong>Specificaţii suplimentare</strong></p>
      <p>HTML:<br>
      Executabil: \xampp\htdocs<br>
      Terminatori acceptaţi: .html .htm<br>
      => pachet de bază</p>
      <p>SSI:<br>
      Executabil: \xampp\htdocs<br>
      Terminatori acceptaţi: .shtml<br>
      => pachet de bază</p>
      <p>CGI:<br>
      Executabil: \xampp\htdocs and \xampp\cgi-bin<br>
      Terminatori acceptaţi: .cgi<br>
      => pachet de bază</p>
      <p>PHP:<br>
      Executabil: \xampp\htdocs and \xampp\cgi-bin<br>
      Terminatori acceptaţi: .php<br>
      => pachet de bază</p>
      <p>Perl:<br>
      Executabil: \xampp\htdocs and \xampp\cgi-bin<br>
      Terminatori acceptaţi: .pl<br>
      => pachet de bază</p>
      <p>Apache::ASP Perl:<br>
      Executabil: \xampp\htdocs<br>
      Terminatori acceptaţi: .asp<br>
      => pachet de bază</p>
      <p>JSP Java:<br>
      Executabil: \xampp\tomcat\webapps\java (e.g.)<br>
      Terminatori acceptaţi: .jsp<br>
      => Tomcat add-on</p>
      <p>Servlets Java:<br>
      Executabil: \xampp\tomcat\webapps\java (e.g.)<br>
      Terminatori acceptaţi: .html (u.a)<br>
      => Tomcat add-on</p>
      </dd>
      <dt>Pot reloca XAMPP instalat?</dt>
      <dd>
      <p>Da. După ce mutaţi directorul XAMPP-ului, trebuie executat "setup_xampp.bat". Cu această ocazie, se vor reconfigura căile în fişierele de configurare.</p>
      <p>Dacă aţi instalat vreun server ca serviciu Windows, mai întâi trebuie să înlăturaţi serviciul, iar după mutare puteţi instala din nou serviciul.</p>
      <p>Atenţiune: Nu sunt modificate fişierele de configurare din scripturile voastre, precum aplicaţiile PHP. Este însă posibilă scrierea unui "plug-in" pentru programul de instalare. Cu un astfel de plug-in, installer-ul poate modifica şi asfel de fişiere.</p>
      </dd>
      <dt>Ce sunt "Paginile automate de start" pentru directoarele WWW?</dt>
      <dd>
      <p>Numele fişierului standard pentru funcţia Apache "DirectoryIndex" este "index.html" sau "index.php". Oricând navigaţi la un folder (de ex. "http://localhost/xampp/"), iar Apache poate găsi acest fişier, îl va afişa pe acesta, în locul unei listări a directorului.</p>
      </dd>
      <dt>De unde pot să schimb configuraţia?</dt>
      <dd>
      <p>Din fişierele de configurare puteţi modifica aproape toate setările din XAMPP. Trebuie doar să deschideţi fişierul într-un editor şi să modificaţi setarea pe care o doriţi. Numai pentru a configura FileZilla şi Mercury este necesară utilizarea aplicaţiei de configurare.</p>
      </dd>

      <dt>De ce nu poate funcţiona XAMPP cu Windows XP SP2?</dt>
      <dd>
      <p>Odată cu Service Pack 2 (SP2), Microsoft livrează un firewall mai bun, care porneşte automat. Acest firewall blochează acum porturile necesare 80 (http) şi 443 (https) , astfel încât Apache-ul nu poate starta.</p>
      <p><strong>Soluţia rapidă:</strong></p>
      <p>Dezactivaţi firewall-ul Microsoft din bara de unelte şi încercaţi din nou să porniţi XAMPP. O soluţionare mai adecvată o reprezintă definirea unei excepţii, cu ajutorul Centrului de Securitate.</p>
      <p><strong>Pentru o funcţionare de bază sunt utilizate următoarele porturi:</strong></p>
      <p>Apache (HTTP): Port 80<br/>
        Apache (WebDAV): Port 81</br>
        Apache (HTTPS): Port 443</br>
        MySQL: Port 3306</br>
        FileZilla (FTP): Port 21</br>
        FileZilla (Admin): Port 14147</br>
        Mercury (SMTP): Port 25</br>
        Mercury (POP3): Port 110</br>
        Mercury (IMAP): Port 143</br>
        Mercury (HTTP): Port 2224</br>
        Mercury (Finger): Port 79</br>
        Mercury (PH): Port 105</br>
        Mercury (PopPass): Port 106</br>
        Tomcat (AJP/1.3): Port 8009</br>
        Tomcat (HTTP): Port 8080</p>
      </dd>

      <dt>De ce nu funcţionează XAMPP cu Vista?</dt>
      <dd>
      <p><strong>Controlul Contului de Utilizator (UAC)</strong></p>
      <p>În directorul "C:\\Program Files" nu aveţi drepturi depline de scriere, chiar fiind Administrator. Sau aveţi doar drepturi limitate (de ex. pentru ".\\xampp\\htdocs"). În acest caz, nu puteţi edita fişierul.</br>
<strong>Soluţie:</strong> Din Explorer, acordaţi-vă drepturi sporite (clic dreapta / securitate) , sau dezactivaţi Controlul Contului de Utilizator (UAC).</p>
      <p>Aţi instalat Apache/MySQL în "C:\\xampp" ca serviciu Windows. Dar nu puteţi porni/opri serviciile din "Panoul de Control XAMPP" sau nu le puteţi dezinstala.</br></br>
<strong>Soluţie:</strong> Folosiţi consola Windows de administrare a serviciilor, sau dezactivaţi UAC.</p>
      <p><strong>Dezactivarea Controlului Cont de Utilizator (UAC)</strong></p>
      <p>Pentru dezactivarea UAC, folosiţi programul "msconfig". În "msconfig" mergeţi la "Tools", selectaţi "dezactivare Control al Contului de Utilizator" şi verificaţi selecţia. După aceasta, Windows-ul trebuie restartat. În acelaşi mod se poate reactiva UAC.</p>
      </dd>

      <dt>Cum realizez verificarea sumei de control md5?</dt>
      <dd>
      <p>Pentru a compara fişiere, adeseori se folosesc sume de control. Un standard pentru a crea această sumă de control md5 (Algoritmul de Rezumare a Mesajului - Message Digest 5).</p>
      <p>Cu această sumă de control md5 puteţi verifica integritatea pachetului XAMPP descărcat. Aveţi nevoie desigur, de un program care să poată construi aceste sume de control. În cazul Windows-ului, aveţi la dispoziţie utilitarul Microsoft:</p>
      <p><a href="http://support.microsoft.com/kb/841290">Disponibilitatea şi descrierea utilitarului de Verificare a Integrităţii Sumei de Control a Fişierului</a></p>
      <p>Desigur, se poate folosi orice alt program care furnizează sume de control md5, precum md5sum de la GNU.</p>
      <p>Odată ce aţi instalat un astfel de program (de ex. fciv.exe), puteţi continua cu următorii paşi:</p>
      <p>
        <ul>
          <li>Descarcă XAMPP (de ex. xampp-win32-1.8.2-0.exe)</li>
          <li>Pentru obţinerea sumei de control:</br>
            <code>fciv.exe xampp-win32-1.8.2-0.exe</code>
          </li>
          <li>Acum puteţi compara această sumă de control, cu cea care se află în pagina gazdă principală a XAMPP pentru Windows.</li>
        </ul>
      </p>
      <p>Dacă cele două sume de control sunt egale, este în regulă. Dacă nu, înseamnă fie că descărcarea este invalidată de erori, sau că fişierul a fost modificat.</p>
      </dd>

      <dt>De ce nu au efect modificările aduse fişierului php.ini?</dt>
      <dd>
      <p>Dacă o modificare a fişierului "php.ini" nu are efect, probabil că PHP îl foloseşte pe un altul. Puteţi verifica aceasta cu phpinfo(). Introduceţi adresa URI  http://localhost/xampp/phpinfo.php şi căutaţi "Fişierul de Configurare Încărcat". Această valoare indică fişierul "php.ini" utilizat de PHP în realitate.</p>
      <p><strong>Observaţie:</strong> După modificarea fişierului "php.ini" Apache va trebui restartat, astfel ca Apache/PHP să-şi poată actualiza setările.</p>
      </dd>

      <dt>Ajutor! XAMPP este virusat!</dt>
      <dd>
      <p>Este posibil ca programe antivirus să alerteze din greşeală că XAMPP este virusat, marcând de obicei fişierul xampp-manager.exe. Vorbim de un pozitiv fals, însemnând că antivirusul identifică în mod eronat un virus, acolo unde el nu există. Înaintea lansării, scanăm antiviral fiecare nouă versiune de XAMPP. În prezent utilizăm <a href="http://www.kaspersky.com/virusscanner">Kapersky Online Virus Scanner</a>. You can also use the online tool <a href="https://www.virustotal.com/">Virus Total</a> for scanning XAMPP or send us an email to security (at) apachefriends (dot) org if you find any issue.</p>
      </dd>

      <dt>Cum îmi configurez aplicaţia antivirus?</dt>
      <dd>
      <p>Pentru funcţionarea întregului pachet de aplicaţii web, am inclus tot felul de servere şi programe suplimentare, astfel încât instalarea XAMPP se face cu un mare număr de fişiere. Instalarea XAMPP poate fi mult încetinită dacă are loc pe un sistem Windows protejat de un antivirus, acesta putând chiar bloca unele dintre servere (de web, sau de baze de date). Pentru ca XAMPP să funcţioneze fără pierderi de performanţă datorate antivirusului, verificaţi următoarele setări:</p>
      <p>
        <ul>
          <li>În firewall, adăugaţi excepţii: pentru Apache, pentru MySQL sau orice alt server.</li>
          <li>Scanarea fişierelor la executare: Dacă aţi activat scanarea antivirus pentru toate fişierele, executabilele serverelor pot incetini.</li>
          <li>Scanarea traficului pentru diferite URL-uri: Dacă dezvoltaţi cu XAMPP pe propria maşină, puteţi exclude în setările antivirus traficul cu gazda - "localhost".</li>
        </ul>
      </p>
      </dd>

      <dt>De ce nu porneşte serverul Apache pe sistemul meu?</dt>
      <dd>
      <p>Această problemă poate avea una dintre următoarele cauze:</p>
      <p>
        <ul>
          <li>Aveţi pornite mai multe Servere HTTP (IIS, Sambar, ZEUS şi aşa mai departe). Doar un singur server poate folosi portul 80. Această problemă este indicată de mesajul de eroare:<br/>
<code>(OS 10048)... make_sock: could not bind to adress 0.0.0.0:80
no listening sockets available, shutting down</code></li>
          <li>Portul 80 este blocat de un alt program, cum ar fi Telefonia de Internet "Skype". În cazul lui "Skype" problema se poate evita, mergând în Skype la Acţiuni -> Opţiuni -> Conexiune -> înlăturarea bifei "foloseşte portul 80 drept port alternativ" şi restartarea Skype. Acum ar trebui să funcţioneze.</li>
          <li>Aveţi un firewall care blochează portul asociat Apache. Nu toate firewall-urile sunt compatibile cu Apache, iar uneori simpla dezactivare a firewall-ului nu este suficientă, ci trebuie să-l dezinstalaţi. Firewall-ul este indicat în mesajul de eroare:<br/>
<code>(OS 10038)Socket operation on non-socket: make_sock: for address 0.0.0.0:80,
apr_socket_opt_set: (SO_KEEPALIVE)</code></li>
        </ul>
        <p>De asemenea, dacă Apache porneşte dar nu vă puteţi conecta la el prin browser, cauza poate fi una dintre următoarele:</p>
        <ul>
          <li>Şi unele programe antivirus pot cauza acest lucru, similar acţiunii unui firewall.</li>
          <li>Aveţi un XP Profesional căruia îi lipseşte Pachetul de Service nr. 1. Pentru XAMPP, vă este necesar cel puţin SP1.</li>
        </ul>
      </p>
      <p><strong>Sfat:</strong> If you have problems with used ports, you can try the tool "xampp-portcheck.exe". Maybe it can help.</p>
      </dd>

      <dt>De ce are CPU o încărcare de sarcină pentru Apache de aproape 99%?</dt>
      <dd>
      <p>Există una din următoarele două variante: Fie CPU este la maxim, fie puteţi conecta browser-ul la server, dar nu se vede nimic (sistemul încearcă fără succes să încarce pagina). În ambele situaţii puteţi găsi în fişierul de logare Apache mesajul următor:</p>
      <p><code>Child: Encountered too many AcceptEx faults accepting client connections.
winnt_mpm: falling back to 'AcceptFilter none'.</code></p>
      <p>MPM revine la o mai sigură punere în aplicare, dar unele cereri ale clientului nu au fost corect procesate. În scopul evitării acestei erori, în fişierul "\\xampp\\apache\\conf\\extra\\httpd-mpm.conf" utilizaţi "AcceptFilter", cu filtrul de acceptare "none".</p>
      </dd>

      <dt>De ce nu sunt afişate imaginile şi foile-de-stil?</dt>
      <dd>
      <p>Uneori apar probleme la afişarea imaginilor şi a paginilor-de-stil. În special dacă aceste fişiere sunt localizate pe un disc de reţea. În această situaţie puteţi activa (sau adăuga) în fişierul "\\xampp\\apache\\conf\\httpd.conf" una dintre următoarele linii:</p>
      <p><code>EnableSendfile off</br>
EnableMMAP off</code></p>
      <p>Această problemă poate să apară şi de la programele de regularizare a lăţimii de bandă, precum NetLimiter.</p>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To configure XAMPP to use the included sendmail.exe binary for email delivery, follow these steps:</p>
        <ul>
          <li>Edit the XAMPP "php.ini" file. Within this file, find the [mail function] section and replace it with the following directives. Change the XAMPP installation path if needed.
          <code>
          sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
          </code>
          </li>
          <li>Edit the XAMPP "sendmail.ini" file. Within this file, find the [sendmail] section and replace it with the following directives:
          <code>
          smtp_server=smtp.gmail.com
          smtp_port=465
          smtp_ssl=auto
          error_logfile=error.log
          auth_username=<EMAIL>
          auth_password=your-gmail-password
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>Restart the Apache server using the XAMPP control panel.
          </li>
        </ul>
        <p>You can now use PHP's mail() function to send email from your application.</p> 
      </dd>
      
      <dt>Cum pot seta o parolă de root în MySQL?</dt>
      <dd>
      <p>Configure it with the "XAMPP Shell" (command prompt). Open the shell from the XAMPP control pane and execute this command:<code>mysqladmin.exe -u root password secret</code>This sets the root password to 'secret'.</p>
      </dd>

      <dt>Pot să-mi folosesc propriul server MySQL?</dt>
      <dd>
      <p>Da. Pur şi simplu nu porniţi MySQL-ul din pachetul XAMPP. Reţineţi că nu pot fi startate două servere pe un acelaşi port. Dacă aţi setat o parolă de "root", nu uitaţi să editaţi fişierul "\\xampp\\phpMyAdmin\\config.ini.php".</p>
      </dd>

      <dt>Cum restricţionez accesul din exterior la phpMyAdmin?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>Înainte de a putea accesa serverul MySQL, phpMyAdmin va solicita un nume de utilizator şi o parolă. Nu uitaţi ca mai întâi de toate, să setaţi pentru utilizatorul "root" o parolă.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
      <p>To enable remote access to phpMyAdmin, follow these steps:</p>
      <ul>
        <li>Edit the apache\conf\extra\httpd-xampp.conf file in your XAMPP installation directory.</li>
        <li>Within this file, find the lines below. 
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require local
          </code></p>
        </li>
        <li>Then replace 'Require local' with 'Require all granted'.</li>
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require all granted
          </code></p>
        <li>Restart the Apache server using the XAMPP control panel.</li>
      </ul>
      </dd>
      
      <dt>Unde se află suportul IMAP pentru PHP?</dt>
      <dd>
      <p>Iniţial, suportul IMAP pentru PHP este dezactivat în XAMPP din cauza unor misterioase erori de iniţializare cu unele versiuni casnice precum Windows98. Dacă lucraţi cu sisteme NT, puteţi deschide fişierul "\\xampp\\php\\php.ini" pentru activarea extensiei php, prin înlăturarea semnului punct şi virgulă de la începutul liniei ";extension=php_imap.dll". Trebuie să fie:</br>
<code>extension=php_imap.dll</code></p>
      <p>Acum restartaţi Apache, iar IMAP ar trebui să funcţioneze. Puteţi folosi aceiaşi paşi pentru fiecare extensie care nu este validată în configuraţia iniţială.</p>
      </dd>

      <dt>De ce unele aplicaţii PHP open source nu funcţionează cu XAMPP pe Windows?</dt>
      <dd>
      <p>O mulţime de aplicaţii PHP sau extensii care au fost scrise pentru Linux, nu au fost portate pe Windows. </p>
      </dd>

      <dt>Pot şterge directorul "install" după instalare?</dt>
      <dd>
      <p>E mai bine să nu. Scripturile de aici sunt încă necesare tuturor pachetelor adiţionale (add-onuri) şi upgrade-urilor lui XAMPP.</p>
      </dd>

      <dt>Cum activez eaccelerator-ul?</dt>
      <dd>
      <p>Ca şi alte extensii (Zend) le puteţi activa în "php.ini". În acest fişier validaţi linia ";zend_extension = "\\xampp\\php\\ext\\php_eaccelerator.dll"". Ar trebui să fie:</br>
<code>zend_extension = "\xampp\php\ext\php_eaccelerator.dll"</code></p>
      </dd>

      <dt>Cum depăşesc o eroare de conectare la serverul meu MySQL?</dt>
      <dd>
      <p>Dacă extensia mssql a fost încărcată în php.ini, uneori apar probleme când este folosit doar TCP/IP. Puteţi rezolva această problemă cu un mai nou "ntwdblib.dll" de la Microsoft. Vă rugăm înlocuiţi vechiul fişier în "\\xampp\\apache\\bin" şi "\\xampp\\php" cu unul nou. Din cauză de licenţă, nu putem împacheta o versiune nouă a acestui fişier cu XAMPP.</p>
      </dd>

      <dt>Cum utilizez extensia PHP mcrypt?</dt>
      <dd>
      <p>Pentru aceasta, am deschis un subiect în forum, cu exemple şi soluţii posibile: <a href="https://community.apachefriends.org/f/viewtopic.php?t=3012">Subiect MCrypt</a></p>
      </dd>

      <dt>Do Microsoft Active Server Pages (ASP) work with XAMPP?</dt>
      <dd>
      <p>Nu. Iar Apache::ASP nu-i acelaşi lucru cu suplimentul Perl. Apache::ASP recunoaşte doar Perl-Script, pe când ASP din Internet Information Server (IIS) recunoaşte de asemenea VBScript-ul obişnuit. Dar pentru ASP.NET este disponibil un modul Apache de la terţi.</p>
      </dd>

      <dt>How can I get XAMPP working on port 80 under Windows 10?</dt>
      <dd>
      <p>By default, Windows 10 starts Microsoft IIS on port 80, which is the same default port used by Apache in XAMPP. As a result, Apache cannot bind to port 80.</p>
      <p>To deactivate IIS from running on port 80, follow these steps:</p>
      <ul>
        <li>Open the Services panel in Computer Management.</li>
        <li>Search for the 'World Wide Web Publishing Service' and select it.</li>
        <li>Click the link to 'Stop the service'.</li>
        <li>Double-click the service name.</li>
        <li>In the 'Startup type' field, change the startup type to 'Disabled'.</li>
        <li>Click 'OK' to save your changes.</li>
      </ul>
      <p>You should now be able to start Apache in XAMPP on port 80.</p>
      <p>For more information, refer to the 'Troubleshoot Apache Startup Problems' guide included with XAMPP or <a href='https://community.apachefriends.org/f/viewtopic.php?f=16&t=71620'>this forum post</a>.</p>
      </dd>
      
      <dt>How can I use Microsoft Edge to access local addresses under Windows 10?</dt>
      <dd>
      <p>If your local machine has the host name 'myhost', you will not be able to access URLs such as http://myhost in Microsoft Edge. To resolve this, you should instead use the addresses http://127.0.0.1 or http://localhost.</p>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: \xampp\apache\conf\httpd.conf, \xampp\apache\conf\extra\httpd-xampp.conf</li>
          <li>PHP configuration file: \xampp\php\php.ini</li>
          <li>MySQL configuration file: \xampp\mysql\bin\my.ini</li>
          <li>FileZilla Server configuration file: \xampp\FileZillaFTP\FileZilla Server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\tomcat\conf\server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\sendmail\sendmail.ini</li>
          <li>Mercury Mail configuration file: \xampp\MercuryMail\MERCURY.INI</li>
        </ul>
      </dd>

    </dl>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Politica de intimitate</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN furnizate de
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
