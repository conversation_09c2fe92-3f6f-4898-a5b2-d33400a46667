<?php
$page_title = "Login";
$page_description = "Sign in to your ModernShop account";
$additional_css = ['css/forms.css', 'css/animations.css'];
$additional_js = ['js/forms.js'];

require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Redirect if already logged in
if (is_logged_in()) {
    if (is_admin()) {
        redirect('admin/index.php');
    } else {
        redirect('index.php');
    }
}

$errors = [];
$username = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Validate CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $errors['general'] = 'Invalid request. Please try again.';
    } else {
        // Validate input
        $validation_errors = validate_login($username, $password);
        
        if (empty($validation_errors)) {
            // Attempt login
            $result = login_user($pdo, $username, $password);
            
            if ($result['success']) {
                set_flash_message('Welcome back, ' . htmlspecialchars($_SESSION['username']) . '!', 'success');
                
                // Redirect based on user role
                if (is_admin()) {
                    redirect('admin/index.php');
                } else {
                    redirect('index.php');
                }
            } else {
                $errors['general'] = $result['message'];
            }
        } else {
            $errors = array_merge($errors, $validation_errors);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="auth-page">
        <div class="auth-container animate-fade-in-up">
            <!-- Header -->
            <div class="auth-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-store"></i>
                    ModernShop
                </a>
                <h1>Welcome Back</h1>
                <p>Sign in to your account to continue shopping</p>
            </div>
            
            <!-- Login Form -->
            <div class="form-container">
                <?php if (!empty($errors['general'])): ?>
                    <div class="alert alert-error animate-shake">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($errors['general']); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" data-validate class="animate-fade-in" style="animation-delay: 0.2s;">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <!-- Username/Email Field -->
                    <div class="form-group <?php echo isset($errors['username']) ? 'error' : ''; ?>">
                        <input 
                            type="text" 
                            name="username" 
                            class="form-input" 
                            value="<?php echo htmlspecialchars($username); ?>"
                            required
                            autocomplete="username"
                        >
                        <label class="form-label">Username or Email</label>
                        <?php if (isset($errors['username'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['username']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Password Field -->
                    <div class="form-group <?php echo isset($errors['password']) ? 'error' : ''; ?>">
                        <input 
                            type="password" 
                            name="password" 
                            class="form-input" 
                            required
                            autocomplete="current-password"
                        >
                        <label class="form-label">Password</label>
                        <?php if (isset($errors['password'])): ?>
                            <div class="form-message error">
                                <?php echo htmlspecialchars($errors['password']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Remember Me -->
                    <div class="form-checkbox">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-animate">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </div>
                </form>
                
                <!-- Divider -->
                <div class="form-divider">
                    <span>or</span>
                </div>
                
                <!-- Register Link -->
                <div class="form-link">
                    <p>Don't have an account? 
                        <a href="register.php" class="hover-glow">Create one now</a>
                    </p>
                </div>
                
                <!-- Forgot Password -->
                <div class="form-link">
                    <a href="#" class="text-sm">Forgot your password?</a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="auth-footer">
                <p>&copy; <?php echo date('Y'); ?> ModernShop. All rights reserved.</p>
                <p>
                    <a href="index.php">Back to Store</a> | 
                    <a href="#">Privacy Policy</a> | 
                    <a href="#">Terms of Service</a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <?php foreach ($additional_js as $js_file): ?>
        <script src="<?php echo $js_file; ?>"></script>
    <?php endforeach; ?>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add focus effects to form inputs
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
            
            // Add hover effect to submit button
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = 'var(--shadow-lg)';
                });
                
                submitBtn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            }
        });
    </script>
</body>
</html>
