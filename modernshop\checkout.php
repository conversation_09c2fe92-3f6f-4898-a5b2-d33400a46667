<?php
$page_title = "Checkout";
$page_description = "Complete your order securely";
$additional_css = ['css/forms.css', 'css/checkout.css', 'css/animations.css'];
$additional_js = ['js/forms.js', 'js/checkout.js'];

require_once 'config/db-sqlite.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login for checkout
require_login();

// Check if cart is empty
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    set_flash_message('Your cart is empty. Please add some items before checkout.', 'warning');
    redirect('index.php');
}

// Get cart items with product details
$cart_items = [];
$cart_total = 0;

$product_ids = array_keys($_SESSION['cart']);
$placeholders = str_repeat('?,', count($product_ids) - 1) . '?';

try {
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
    $stmt->execute($product_ids);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($products as $product) {
        $cart_item = $_SESSION['cart'][$product['id']];
        $item_total = $product['price'] * $cart_item['quantity'];
        
        $cart_items[] = [
            'product' => $product,
            'quantity' => $cart_item['quantity'],
            'item_total' => $item_total
        ];
        
        $cart_total += $item_total;
    }
} catch (Exception $e) {
    set_flash_message('Error loading cart items.', 'error');
    redirect('cart.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verify_csrf_token($csrf_token)) {
        set_flash_message('Invalid request. Please try again.', 'error');
        redirect('checkout.php');
    }
    
    // Get form data
    $shipping_data = [
        'full_name' => sanitize_input($_POST['full_name'] ?? ''),
        'email' => sanitize_input($_POST['email'] ?? ''),
        'phone' => sanitize_input($_POST['phone'] ?? ''),
        'address' => sanitize_input($_POST['address'] ?? ''),
        'city' => sanitize_input($_POST['city'] ?? ''),
        'state' => sanitize_input($_POST['state'] ?? ''),
        'postal_code' => sanitize_input($_POST['postal_code'] ?? ''),
        'country' => sanitize_input($_POST['country'] ?? '')
    ];
    
    $payment_method = sanitize_input($_POST['payment_method'] ?? '');
    
    // Basic validation
    $errors = [];
    foreach ($shipping_data as $key => $value) {
        if (empty($value)) {
            $errors[] = ucfirst(str_replace('_', ' ', $key)) . ' is required';
        }
    }
    
    if (empty($payment_method)) {
        $errors[] = 'Payment method is required';
    }
    
    if (empty($errors)) {
        try {
            $pdo->beginTransaction();
            
            // Create orders for each cart item
            $order_ids = [];
            foreach ($cart_items as $item) {
                $stmt = $pdo->prepare("
                    INSERT INTO orders (user_id, product_id, quantity, total_price) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_SESSION['user_id'],
                    $item['product']['id'],
                    $item['quantity'],
                    $item['item_total']
                ]);
                
                $order_ids[] = $pdo->lastInsertId();
                
                // Update product stock
                $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
                $stmt->execute([$item['quantity'], $item['product']['id']]);
            }
            
            $pdo->commit();
            
            // Clear cart
            clear_cart();
            
            // Store order info in session for success page
            $_SESSION['last_order'] = [
                'order_ids' => $order_ids,
                'total' => $cart_total,
                'shipping' => $shipping_data,
                'payment_method' => $payment_method
            ];
            
            set_flash_message('Order placed successfully!', 'success');
            redirect('order-success.php');
            
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash_message('Error processing order. Please try again.', 'error');
        }
    } else {
        foreach ($errors as $error) {
            set_flash_message($error, 'error');
        }
    }
}

include 'includes/header.php';
?>

<div class="checkout-container animate-fade-in-up">
    <!-- Progress Steps -->
    <div class="checkout-progress">
        <div class="progress-step active">
            <div class="progress-step-circle">1</div>
            <div class="progress-step-label">Shipping</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-circle">2</div>
            <div class="progress-step-label">Review</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-circle">3</div>
            <div class="progress-step-label">Payment</div>
        </div>
    </div>
    
    <form method="POST" class="checkout-form" data-validate>
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
        
        <div class="checkout-grid">
            <!-- Main Checkout Form -->
            <div class="checkout-main">
                <!-- Step 1: Shipping Information -->
                <div id="step-1" class="checkout-step active">
                    <div class="checkout-section">
                        <h2 class="checkout-section-title">
                            <i class="fas fa-shipping-fast"></i>
                            Shipping Information
                        </h2>
                        
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <input type="text" name="full_name" class="form-input" required 
                                       value="<?php echo htmlspecialchars($_SESSION['full_name'] ?? ''); ?>">
                                <label class="form-label">Full Name</label>
                            </div>
                            
                            <div class="form-group">
                                <input type="email" name="email" class="form-input" required>
                                <label class="form-label">Email Address</label>
                            </div>
                            
                            <div class="form-group">
                                <input type="tel" name="phone" class="form-input" required>
                                <label class="form-label">Phone Number</label>
                            </div>
                            
                            <div class="form-group full-width">
                                <input type="text" name="address" class="form-input" required>
                                <label class="form-label">Street Address</label>
                            </div>
                            
                            <div class="form-group">
                                <input type="text" name="city" class="form-input" required>
                                <label class="form-label">City</label>
                            </div>
                            
                            <div class="form-group">
                                <input type="text" name="state" class="form-input" required>
                                <label class="form-label">State/Province</label>
                            </div>
                            
                            <div class="form-group">
                                <input type="text" name="postal_code" class="form-input" required>
                                <label class="form-label">Postal Code</label>
                            </div>
                            
                            <div class="form-group">
                                <select name="country" class="form-select" required>
                                    <option value="">Select Country</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="UK">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                </select>
                                <label class="form-label">Country</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Review Order -->
                <div id="step-2" class="checkout-step">
                    <div class="checkout-section">
                        <h2 class="checkout-section-title">
                            <i class="fas fa-eye"></i>
                            Review Your Order
                        </h2>
                        
                        <div class="order-review">
                            <?php foreach ($cart_items as $item): ?>
                                <div class="order-item">
                                    <img src="<?php echo get_product_image($item['product']['image']); ?>" 
                                         alt="<?php echo htmlspecialchars($item['product']['name']); ?>"
                                         class="order-item-image">
                                    <div class="order-item-details">
                                        <div class="order-item-name">
                                            <?php echo htmlspecialchars($item['product']['name']); ?>
                                        </div>
                                        <div class="order-item-quantity">
                                            Quantity: <?php echo $item['quantity']; ?>
                                        </div>
                                    </div>
                                    <div class="order-item-price">
                                        <?php echo format_price($item['item_total']); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Payment -->
                <div id="step-3" class="checkout-step">
                    <div class="checkout-section">
                        <h2 class="checkout-section-title">
                            <i class="fas fa-credit-card"></i>
                            Payment Method
                        </h2>
                        
                        <div class="payment-methods">
                            <div class="payment-method">
                                <input type="radio" name="payment_method" value="credit_card" id="credit_card">
                                <div class="payment-method-info">
                                    <div class="payment-method-name">Credit Card</div>
                                    <div class="payment-method-desc">Pay securely with your credit card</div>
                                </div>
                                <div class="payment-method-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                            </div>
                            
                            <div class="payment-method">
                                <input type="radio" name="payment_method" value="paypal" id="paypal">
                                <div class="payment-method-info">
                                    <div class="payment-method-name">PayPal</div>
                                    <div class="payment-method-desc">Pay with your PayPal account</div>
                                </div>
                                <div class="payment-method-icon">
                                    <i class="fab fa-paypal"></i>
                                </div>
                            </div>
                            
                            <div class="payment-method">
                                <input type="radio" name="payment_method" value="cash_on_delivery" id="cod">
                                <div class="payment-method-info">
                                    <div class="payment-method-name">Cash on Delivery</div>
                                    <div class="payment-method-desc">Pay when you receive your order</div>
                                </div>
                                <div class="payment-method-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Credit Card Fields (hidden by default) -->
                        <div class="credit-card-fields" style="display: none; margin-top: var(--space-6);">
                            <div class="form-grid">
                                <div class="form-group full-width">
                                    <input type="text" name="card_number" class="form-input" placeholder="1234 5678 9012 3456">
                                    <label class="form-label">Card Number</label>
                                </div>
                                
                                <div class="form-group">
                                    <input type="text" name="expiry_date" class="form-input" placeholder="MM/YY">
                                    <label class="form-label">Expiry Date</label>
                                </div>
                                
                                <div class="form-group">
                                    <input type="text" name="cvv" class="form-input" placeholder="123">
                                    <label class="form-label">CVV</label>
                                </div>
                                
                                <div class="form-group full-width">
                                    <input type="text" name="card_name" class="form-input">
                                    <label class="form-label">Name on Card</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="checkout-navigation">
                    <button type="button" class="btn btn-secondary btn-prev checkout-nav-btn" onclick="prevStep()">
                        <i class="fas fa-arrow-left"></i>
                        Previous
                    </button>
                    
                    <button type="button" class="btn btn-primary btn-next checkout-nav-btn" onclick="nextStep()">
                        Next
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    
                    <button type="submit" class="btn btn-success btn-submit checkout-nav-btn" onclick="submitOrder()" style="display: none;">
                        <i class="fas fa-check"></i>
                        Place Order
                    </button>
                </div>
            </div>
            
            <!-- Order Summary Sidebar -->
            <div class="order-summary animate-fade-in-right">
                <h3 class="order-summary-title">
                    <i class="fas fa-receipt"></i>
                    Order Summary
                </h3>
                
                <div class="order-items">
                    <?php foreach ($cart_items as $item): ?>
                        <div class="order-item">
                            <img src="<?php echo get_product_image($item['product']['image']); ?>" 
                                 alt="<?php echo htmlspecialchars($item['product']['name']); ?>"
                                 class="order-item-image">
                            <div class="order-item-details">
                                <div class="order-item-name">
                                    <?php echo htmlspecialchars($item['product']['name']); ?>
                                </div>
                                <div class="order-item-quantity">
                                    Qty: <?php echo $item['quantity']; ?>
                                </div>
                            </div>
                            <div class="order-item-price">
                                <?php echo format_price($item['item_total']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="order-totals">
                    <div class="order-total-row">
                        <span>Subtotal</span>
                        <span><?php echo format_price($cart_total); ?></span>
                    </div>
                    <div class="order-total-row">
                        <span>Shipping</span>
                        <span style="color: var(--color-success);">FREE</span>
                    </div>
                    <div class="order-total-row">
                        <span>Tax</span>
                        <span>$0.00</span>
                    </div>
                    <div class="order-total-row final">
                        <span>Total</span>
                        <span><?php echo format_price($cart_total); ?></span>
                    </div>
                </div>
                
                <!-- Security Info -->
                <div style="text-align: center; margin-top: var(--space-6); padding-top: var(--space-4); border-top: 1px solid var(--color-gray-200);">
                    <div style="color: var(--color-gray-500); font-size: var(--font-size-sm);">
                        <i class="fas fa-shield-alt" style="color: var(--color-success);"></i>
                        Secure 256-bit SSL encryption
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?php include 'includes/footer.php'; ?>
