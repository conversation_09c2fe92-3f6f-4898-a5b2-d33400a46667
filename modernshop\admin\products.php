<?php
$page_title = "Manage Products";
$page_description = "Add, edit, and manage your product catalog";
$additional_css = ['../css/admin.css', '../css/forms.css', '../css/animations.css'];
$additional_js = ['../js/forms.js'];

require_once '../config/db-sqlite.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

$action = $_GET['action'] ?? 'list';
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verify_csrf_token($csrf_token)) {
        set_flash_message('Invalid request. Please try again.', 'error');
        redirect('products.php');
    }
    
    $name = sanitize_input($_POST['name'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    $price = (float)($_POST['price'] ?? 0);
    $stock = (int)($_POST['stock'] ?? 0);
    $category_id = (int)($_POST['category_id'] ?? 0);
    
    if ($action === 'add') {
        try {
            $stmt = $pdo->prepare("INSERT INTO products (name, description, price, stock, category_id) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$name, $description, $price, $stock, $category_id]);
            
            set_flash_message('Product added successfully!', 'success');
            redirect('products.php');
        } catch (Exception $e) {
            set_flash_message('Error adding product: ' . $e->getMessage(), 'error');
        }
    } elseif ($action === 'edit' && $product_id > 0) {
        try {
            $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, stock = ?, category_id = ? WHERE id = ?");
            $stmt->execute([$name, $description, $price, $stock, $category_id, $product_id]);
            
            set_flash_message('Product updated successfully!', 'success');
            redirect('products.php');
        } catch (Exception $e) {
            set_flash_message('Error updating product: ' . $e->getMessage(), 'error');
        }
    }
}

// Handle delete action
if ($action === 'delete' && $product_id > 0) {
    try {
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        
        set_flash_message('Product deleted successfully!', 'success');
        redirect('products.php');
    } catch (Exception $e) {
        set_flash_message('Error deleting product: ' . $e->getMessage(), 'error');
        redirect('products.php');
    }
}

// Get categories for dropdown
try {
    $categories_stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $categories_stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
}

// Get product for editing
$product = null;
if ($action === 'edit' && $product_id > 0) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) {
            set_flash_message('Product not found.', 'error');
            redirect('products.php');
        }
    } catch (Exception $e) {
        set_flash_message('Error loading product.', 'error');
        redirect('products.php');
    }
}

// Get all products for listing
$products = [];
if ($action === 'list') {
    try {
        $stmt = $pdo->query("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            ORDER BY p.id DESC
        ");
        $products = $stmt->fetchAll();
    } catch (Exception $e) {
        set_flash_message('Error loading products.', 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop Admin</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Admin Header -->
            <div class="admin-header">
                <a href="index.php" class="admin-logo">
                    <i class="fas fa-store"></i>
                    ModernShop Admin
                </a>
                
                <div class="admin-user-info">
                    <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="admin-user-role">Administrator</div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="admin-nav">
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <a href="index.php" class="admin-nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="../index.php" class="admin-nav-item">
                        <i class="fas fa-external-link-alt"></i>
                        View Store
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Catalog</div>
                    <a href="products.php" class="admin-nav-item active">
                        <i class="fas fa-box"></i>
                        Products
                    </a>
                    <a href="categories.php" class="admin-nav-item">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Sales</div>
                    <a href="orders.php" class="admin-nav-item">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                    <a href="customers.php" class="admin-nav-item">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <a href="../logout.php" class="admin-nav-item">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Flash Messages -->
            <?php display_flash_message(); ?>
            
            <?php if ($action === 'list'): ?>
                <!-- Products List -->
                <div class="admin-page-header animate-fade-in-down">
                    <h1 class="admin-page-title">Products</h1>
                    <div class="admin-page-actions">
                        <a href="products.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Add Product
                        </a>
                    </div>
                </div>
                
                <!-- Products Table -->
                <div class="data-table-container animate-fade-in-up">
                    <div class="data-table-header">
                        <h2 class="data-table-title">All Products (<?php echo count($products); ?>)</h2>
                    </div>
                    
                    <?php if (empty($products)): ?>
                        <div class="admin-empty">
                            <i class="fas fa-box"></i>
                            <h3>No products yet</h3>
                            <p>Start by adding your first product to the catalog.</p>
                            <a href="products.php?action=add" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add First Product
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <span style="font-weight: var(--font-weight-semibold);">
                                                #<?php echo $product['id']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div style="font-weight: var(--font-weight-semibold);">
                                                <?php echo htmlspecialchars($product['name']); ?>
                                            </div>
                                            <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                                                <?php echo htmlspecialchars(truncate_text($product['description'], 50)); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($product['category_name']): ?>
                                                <span class="badge badge-primary">
                                                    <?php echo htmlspecialchars($product['category_name']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span style="color: var(--color-gray-500);">No category</span>
                                            <?php endif; ?>
                                        </td>
                                        <td style="font-weight: var(--font-weight-semibold); color: var(--color-primary);">
                                            <?php echo format_price($product['price']); ?>
                                        </td>
                                        <td>
                                            <span class="status-badge <?php echo $product['stock'] > 5 ? 'active' : ($product['stock'] > 0 ? 'pending' : 'inactive'); ?>">
                                                <?php echo $product['stock']; ?> units
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge active">
                                                <i class="fas fa-check"></i>
                                                Active
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="../product.php?id=<?php echo $product['id']; ?>" class="btn btn-secondary btn-icon" title="View Product" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="products.php?action=edit&id=<?php echo $product['id']; ?>" class="btn btn-primary btn-icon" title="Edit Product">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="products.php?action=delete&id=<?php echo $product['id']; ?>" class="btn btn-danger btn-icon" title="Delete Product" onclick="return confirm('Are you sure you want to delete this product?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
                
            <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Add/Edit Product Form -->
                <div class="admin-page-header animate-fade-in-down">
                    <h1 class="admin-page-title">
                        <?php echo $action === 'add' ? 'Add New Product' : 'Edit Product'; ?>
                    </h1>
                    <div class="admin-page-actions">
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            Back to Products
                        </a>
                    </div>
                </div>
                
                <div class="admin-form animate-fade-in-up">
                    <div class="admin-form-header">
                        <h2 class="admin-form-title">
                            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
                            Product Information
                        </h2>
                    </div>
                    
                    <form method="POST" data-validate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="admin-form-grid">
                            <!-- Product Name -->
                            <div class="form-group">
                                <input type="text" name="name" class="form-input" required 
                                       value="<?php echo htmlspecialchars($product['name'] ?? ''); ?>">
                                <label class="form-label">Product Name</label>
                            </div>
                            
                            <!-- Category -->
                            <div class="form-group">
                                <select name="category_id" class="form-select" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo ($product['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <label class="form-label">Category</label>
                            </div>
                            
                            <!-- Price -->
                            <div class="form-group">
                                <input type="number" name="price" class="form-input" step="0.01" min="0" required 
                                       value="<?php echo $product['price'] ?? ''; ?>">
                                <label class="form-label">Price ($)</label>
                            </div>
                            
                            <!-- Stock -->
                            <div class="form-group">
                                <input type="number" name="stock" class="form-input" min="0" required 
                                       value="<?php echo $product['stock'] ?? ''; ?>">
                                <label class="form-label">Stock Quantity</label>
                            </div>
                            
                            <!-- Description -->
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <textarea name="description" class="form-input" rows="4" required 
                                          data-auto-resize><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                                <label class="form-label">Description</label>
                            </div>
                        </div>
                        
                        <!-- Form Actions -->
                        <div style="margin-top: var(--space-8); display: flex; gap: var(--space-4); justify-content: flex-end;">
                            <a href="products.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?php echo $action === 'add' ? 'Add Product' : 'Update Product'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <?php foreach ($additional_js as $js_file): ?>
        <script src="<?php echo $js_file; ?>"></script>
    <?php endforeach; ?>
    
    <script>
        // Add table row hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const tableRows = document.querySelectorAll('.data-table tbody tr');
            
            tableRows.forEach((row, index) => {
                // Stagger animation
                row.style.opacity = '0';
                row.style.transform = 'translateY(10px)';
                row.style.animation = `fadeInUp 0.4s ease-out forwards`;
                row.style.animationDelay = `${index * 0.05}s`;
                
                // Enhanced hover effect
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'var(--color-primary-50)';
                    this.style.transform = 'scale(1.01)';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
