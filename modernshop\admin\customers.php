<?php
$page_title = "Manage Customers";
$page_description = "View and manage customer accounts";
$additional_css = ['../css/admin.css', '../css/animations.css'];

require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require admin access
require_admin();

// Get all customers with order statistics
try {
    $stmt = $pdo->query("
        SELECT u.*, 
               COUNT(DISTINCT o.id) as total_orders,
               COALESCE(SUM(o.total_price), 0) as total_spent,
               MAX(o.order_date) as last_order_date
        FROM users u 
        LEFT JOIN orders o ON u.id = o.user_id 
        WHERE u.role = 'user'
        GROUP BY u.id 
        ORDER BY u.created_at DESC
    ");
    $customers = $stmt->fetchAll();
} catch (Exception $e) {
    $customers = [];
    set_flash_message('Error loading customers.', 'error');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - ModernShop Admin</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <?php foreach ($additional_css as $css_file): ?>
        <link rel="stylesheet" href="<?php echo $css_file; ?>">
    <?php endforeach; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <!-- Admin Header -->
            <div class="admin-header">
                <a href="index.php" class="admin-logo">
                    <i class="fas fa-store"></i>
                    ModernShop Admin
                </a>
                
                <div class="admin-user-info">
                    <div class="admin-user-name"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="admin-user-role">Administrator</div>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="admin-nav">
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Main</div>
                    <a href="index.php" class="admin-nav-item">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="../index.php" class="admin-nav-item">
                        <i class="fas fa-external-link-alt"></i>
                        View Store
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Catalog</div>
                    <a href="products.php" class="admin-nav-item">
                        <i class="fas fa-box"></i>
                        Products
                    </a>
                    <a href="categories.php" class="admin-nav-item">
                        <i class="fas fa-tags"></i>
                        Categories
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Sales</div>
                    <a href="orders.php" class="admin-nav-item">
                        <i class="fas fa-shopping-cart"></i>
                        Orders
                    </a>
                    <a href="customers.php" class="admin-nav-item active">
                        <i class="fas fa-users"></i>
                        Customers
                    </a>
                </div>
                
                <div class="admin-nav-section">
                    <div class="admin-nav-title">Account</div>
                    <a href="../logout.php" class="admin-nav-item">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Flash Messages -->
            <?php display_flash_message(); ?>
            
            <!-- Page Header -->
            <div class="admin-page-header animate-fade-in-down">
                <h1 class="admin-page-title">Customers</h1>
                <div class="admin-page-actions">
                    <button class="btn btn-secondary" onclick="exportCustomers()">
                        <i class="fas fa-download"></i>
                        Export Customers
                    </button>
                </div>
            </div>
            
            <!-- Customer Statistics -->
            <div class="dashboard-grid animate-fade-in-up" style="margin-bottom: var(--space-8);">
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Total Customers</div>
                        <div class="dashboard-card-icon primary">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value"><?php echo count($customers); ?></div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +15% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Active Customers</div>
                        <div class="dashboard-card-icon success">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value">
                        <?php 
                        $active_customers = array_filter($customers, function($c) { 
                            return $c['total_orders'] > 0; 
                        });
                        echo count($active_customers); 
                        ?>
                    </div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +8% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">Average Spent</div>
                        <div class="dashboard-card-icon warning">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value">
                        <?php 
                        $total_spent = array_sum(array_column($customers, 'total_spent'));
                        $avg_spent = count($active_customers) > 0 ? $total_spent / count($active_customers) : 0;
                        echo format_price($avg_spent); 
                        ?>
                    </div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +12% from last month
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-title">New This Month</div>
                        <div class="dashboard-card-icon error">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-value">
                        <?php 
                        $new_customers = array_filter($customers, function($c) { 
                            return strtotime($c['created_at']) > strtotime('-30 days'); 
                        });
                        echo count($new_customers); 
                        ?>
                    </div>
                    <div class="dashboard-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +25% from last month
                    </div>
                </div>
            </div>
            
            <!-- Customers Table -->
            <?php if (empty($customers)): ?>
                <div class="data-table-container animate-fade-in-up">
                    <div class="admin-empty">
                        <i class="fas fa-users"></i>
                        <h3>No customers yet</h3>
                        <p>Customers will appear here when they register on your store.</p>
                        <a href="../index.php" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i>
                            View Store
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="data-table-container animate-fade-in-up">
                    <div class="data-table-header">
                        <h2 class="data-table-title">All Customers (<?php echo count($customers); ?>)</h2>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Contact</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Last Order</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: var(--space-3);">
                                            <div style="width: 40px; height: 40px; background: var(--color-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: var(--font-weight-bold);">
                                                <?php echo strtoupper(substr($customer['full_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <div style="font-weight: var(--font-weight-semibold); color: var(--color-gray-900);">
                                                    <?php echo htmlspecialchars($customer['full_name']); ?>
                                                </div>
                                                <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                                                    @<?php echo htmlspecialchars($customer['username']); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div style="color: var(--color-gray-900);">
                                                <i class="fas fa-envelope" style="margin-right: var(--space-2); color: var(--color-gray-500);"></i>
                                                <?php echo htmlspecialchars($customer['email']); ?>
                                            </div>
                                            <div style="font-size: var(--font-size-sm); color: var(--color-gray-600); margin-top: var(--space-1);">
                                                <i class="fas fa-calendar" style="margin-right: var(--space-2);"></i>
                                                Joined <?php echo date('M j, Y', strtotime($customer['created_at'])); ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="text-align: center;">
                                            <div style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                                                <?php echo $customer['total_orders']; ?>
                                            </div>
                                            <div style="font-size: var(--font-size-xs); color: var(--color-gray-600);">
                                                orders
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="font-weight: var(--font-weight-semibold); color: var(--color-success);">
                                            <?php echo format_price($customer['total_spent']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($customer['last_order_date']): ?>
                                            <div style="color: var(--color-gray-900);">
                                                <?php echo date('M j, Y', strtotime($customer['last_order_date'])); ?>
                                            </div>
                                            <div style="font-size: var(--font-size-sm); color: var(--color-gray-600);">
                                                <?php echo time_ago($customer['last_order_date']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span style="color: var(--color-gray-500); font-style: italic;">No orders</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($customer['total_orders'] > 0): ?>
                                            <span class="status-badge active">
                                                <i class="fas fa-check"></i>
                                                Active
                                            </span>
                                        <?php else: ?>
                                            <span class="status-badge inactive">
                                                <i class="fas fa-clock"></i>
                                                Inactive
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-secondary btn-icon" title="View Customer Details" onclick="viewCustomer(<?php echo $customer['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-primary btn-icon" title="Send Email" onclick="emailCustomer('<?php echo htmlspecialchars($customer['email']); ?>')">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="btn btn-outline btn-icon" title="View Orders" onclick="viewCustomerOrders(<?php echo $customer['id']; ?>)">
                                                <i class="fas fa-shopping-cart"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    
    <script>
        // Customer management functions
        function viewCustomer(customerId) {
            showNotification(`Viewing customer details for ID: ${customerId}`, 'info');
        }
        
        function emailCustomer(email) {
            window.location.href = `mailto:${email}`;
        }
        
        function viewCustomerOrders(customerId) {
            showNotification(`Viewing orders for customer ID: ${customerId}`, 'info');
        }
        
        function exportCustomers() {
            showNotification('Export feature coming soon', 'info');
        }
        
        // Add animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Stagger animation for table rows
            const tableRows = document.querySelectorAll('.data-table tbody tr');
            tableRows.forEach((row, index) => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(10px)';
                row.style.animation = `fadeInUp 0.4s ease-out forwards`;
                row.style.animationDelay = `${index * 0.05}s`;
                
                // Enhanced hover effect
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'var(--color-primary-50)';
                    this.style.transform = 'scale(1.01)';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // Dashboard cards animation
            const dashboardCards = document.querySelectorAll('.dashboard-card');
            dashboardCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.animation = `fadeInUp 0.6s ease-out forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
