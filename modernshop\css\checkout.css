/* Checkout Process Styling */

/* Checkout Container */
.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-4);
}

/* Progress Steps */
.checkout-progress {
    display: flex;
    justify-content: center;
    margin-bottom: var(--space-12);
    position: relative;
}

.checkout-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 25%;
    right: 25%;
    height: 2px;
    background: var(--color-gray-300);
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: var(--color-white);
    padding: 0 var(--space-4);
}

.progress-step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--color-gray-300);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-2);
    transition: all var(--transition-normal);
}

.progress-step.active .progress-step-circle {
    background: var(--color-primary);
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
}

.progress-step.completed .progress-step-circle {
    background: var(--color-success);
}

.progress-step-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-600);
    text-align: center;
}

.progress-step.active .progress-step-label {
    color: var(--color-primary);
}

.progress-step.completed .progress-step-label {
    color: var(--color-success);
}

/* Checkout Steps */
.checkout-step {
    display: none;
    animation: fadeInUp 0.5s ease-out;
}

.checkout-step.active {
    display: block;
}

/* Checkout Grid */
.checkout-grid {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--space-8);
    margin-top: var(--space-8);
}

@media (max-width: 1024px) {
    .checkout-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
}

/* Checkout Form */
.checkout-form {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--space-8);
}

.checkout-section {
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--color-gray-200);
}

.checkout-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.checkout-section-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-grid .form-group.full-width {
    grid-column: 1 / -1;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

/* Payment Methods */
.payment-methods {
    display: grid;
    gap: var(--space-3);
}

.payment-method {
    display: flex;
    align-items: center;
    padding: var(--space-4);
    border: 2px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.payment-method:hover {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
}

.payment-method.selected {
    border-color: var(--color-primary);
    background: var(--color-primary-50);
}

.payment-method input[type="radio"] {
    margin-right: var(--space-3);
}

.payment-method-info {
    flex: 1;
}

.payment-method-name {
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
}

.payment-method-desc {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
    margin-top: var(--space-1);
}

.payment-method-icon {
    font-size: var(--font-size-2xl);
    color: var(--color-primary);
}

/* Order Summary Sidebar */
.order-summary {
    background: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--space-6);
    height: fit-content;
    position: sticky;
    top: var(--space-20);
}

.order-summary-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.order-item {
    display: flex;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--color-gray-200);
}

.order-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.order-item-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-md);
    flex-shrink: 0;
}

.order-item-details {
    flex: 1;
}

.order-item-name {
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-900);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-1);
}

.order-item-quantity {
    font-size: var(--font-size-xs);
    color: var(--color-gray-600);
}

.order-item-price {
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
    font-size: var(--font-size-sm);
}

/* Order Totals */
.order-totals {
    margin-top: var(--space-6);
    padding-top: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
}

.order-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
}

.order-total-row.final {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    padding-top: var(--space-3);
    border-top: 1px solid var(--color-gray-300);
    margin-top: var(--space-3);
}

/* Checkout Navigation */
.checkout-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--color-gray-200);
}

.checkout-nav-btn {
    min-width: 120px;
}

/* Success Page */
.success-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: var(--space-12) var(--space-4);
}

.success-icon {
    width: 80px;
    height: 80px;
    background: var(--color-success);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    animation: bounce 0.6s ease-out;
}

.success-icon i {
    font-size: var(--font-size-3xl);
    color: var(--color-white);
}

.success-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.success-message {
    font-size: var(--font-size-lg);
    color: var(--color-gray-600);
    margin-bottom: var(--space-8);
    line-height: var(--line-height-relaxed);
}

.order-details-card {
    background: var(--color-gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin: var(--space-8) 0;
    text-align: left;
}

.order-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
}

.order-detail-row:last-child {
    margin-bottom: 0;
    padding-top: var(--space-3);
    border-top: 1px solid var(--color-gray-300);
    font-weight: var(--font-weight-bold);
}

/* Loading States */
.checkout-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.checkout-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-gray-200);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-container {
        padding: var(--space-4);
    }
    
    .checkout-form {
        padding: var(--space-6);
    }
    
    .order-summary {
        padding: var(--space-4);
        position: static;
    }
    
    .checkout-progress {
        margin-bottom: var(--space-8);
    }
    
    .progress-step-label {
        font-size: var(--font-size-xs);
    }
    
    .checkout-navigation {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .checkout-nav-btn {
        width: 100%;
    }
}
