# ModernShop E-commerce Platform

A complete, modern e-commerce platform built with PHP, MySQL, and advanced frontend technologies.

## 🚀 Features

- **Modern UI/UX**: Beautiful, responsive design with smooth animations
- **User Authentication**: Secure login/registration system
- **Product Management**: Complete CRUD operations for products
- **Shopping Cart**: Real-time cart updates with session persistence
- **Checkout System**: Multi-step checkout with payment integration
- **Admin Panel**: Professional admin interface for management
- **Advanced Search**: Real-time search with filters and suggestions
- **Analytics**: Comprehensive tracking and A/B testing
- **Security**: Rate limiting, CSRF protection, input validation
- **Performance**: Caching, lazy loading, image optimization
- **REST API**: RESTful endpoints for integrations

## 📋 Requirements

- **PHP 7.4+** (with extensions: PDO, GD, OpenSSL, JSON)
- **MySQL 5.7+** or **MariaDB 10.2+**
- **Web Server** (Apache/Nginx) or PHP built-in server
- **Composer** (optional, for dependencies)

## 🛠️ Installation

### Step 1: Clone/Download the Project
```bash
# If using Git
git clone <repository-url> modernshop
cd modernshop

# Or download and extract the ZIP file
```

### Step 2: Database Setup
1. Create a MySQL database named `modernshop`
2. Import the database schema:
```sql
-- Import main schema
mysql -u root -p modernshop < database/schema.sql

-- Import advanced features schema
mysql -u root -p modernshop < database/advanced_schema.sql
```

### Step 3: Configuration
1. Update database credentials in `config/db.php`:
```php
$host = 'localhost';
$dbname = 'modernshop';
$username = 'root';
$password = 'your_password';
```

2. Update site configuration in `config/config.php` if needed

### Step 4: Set Permissions
```bash
# Make sure these directories are writable
chmod 755 uploads/
chmod 755 cache/
chmod 755 logs/
```

### Step 5: Run the Application

#### Option A: Using PHP Built-in Server (Recommended for Development)
```bash
php -S localhost:8000
```
Then open: http://localhost:8000

#### Option B: Using XAMPP/WAMP
1. Copy the project to your web server directory (htdocs/www)
2. Start Apache and MySQL
3. Open: http://localhost/modernshop

#### Option C: Using Apache/Nginx
Configure your web server to point to the project directory

## 🔧 Default Admin Account

After running the database setup, you can create an admin account:

1. Register a new account through the website
2. Manually update the user role in the database:
```sql
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

Or use the provided admin seeder:
```bash
php database/create_admin.php
```

## 📁 Project Structure

```
modernshop/
├── admin/                  # Admin panel pages
├── api/                    # REST API endpoints
├── config/                 # Configuration files
├── css/                    # Stylesheets
├── database/               # Database schemas and migrations
├── error-pages/            # Custom error pages
├── images/                 # Static images
├── includes/               # PHP includes and classes
├── js/                     # JavaScript files
├── uploads/                # User uploaded files
├── cache/                  # Cache directory
├── logs/                   # Log files
├── index.php              # Homepage
├── login.php              # Login page
├── register.php           # Registration page
├── product.php            # Product detail page
├── cart.php               # Shopping cart
├── checkout.php           # Checkout process
├── search.php             # Search results
└── README.md              # This file
```

## 🎯 Usage

### For Customers:
1. **Browse Products**: Visit the homepage to see featured products
2. **Search**: Use the search bar with real-time suggestions
3. **Product Details**: Click on any product to view details
4. **Add to Cart**: Add products to your shopping cart
5. **Checkout**: Complete your purchase with the multi-step checkout
6. **Account**: Register/login to track orders

### For Administrators:
1. **Login**: Access `/admin/` with admin credentials
2. **Dashboard**: View sales statistics and recent activity
3. **Products**: Add, edit, or delete products
4. **Orders**: Manage customer orders
5. **Customers**: View customer information
6. **Categories**: Organize products into categories

## 🔌 API Usage

The platform includes a REST API for integrations:

### Get Products
```bash
GET /api/products.php
GET /api/products.php?page=1&limit=12&search=laptop
```

### Get Single Product
```bash
GET /api/products.php/123
```

### Admin Operations (Requires Authentication)
```bash
POST /api/products.php    # Create product
PUT /api/products.php/123 # Update product
DELETE /api/products.php/123 # Delete product
```

## 🎨 Customization

### Styling
- Main styles: `css/main.css`
- Advanced features: `css/advanced.css`
- Animations: `css/animations.css`
- Forms: `css/forms.css`
- Admin panel: `css/admin.css`

### Configuration
- Site settings: `config/config.php`
- Database: `config/db.php`
- Features: Enable/disable features using feature flags

### Adding New Features
1. Create new PHP files in appropriate directories
2. Add database tables if needed
3. Update navigation in `includes/header.php`
4. Add corresponding CSS/JS files

## 🔒 Security Features

- **CSRF Protection**: All forms include CSRF tokens
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Prepared statements throughout
- **Rate Limiting**: API and form submission rate limiting
- **Session Security**: Secure session handling
- **File Upload Security**: Validated file uploads
- **Password Hashing**: Argon2ID password hashing

## 📊 Analytics & Tracking

- **Event Tracking**: User actions and behaviors
- **A/B Testing**: Built-in A/B testing framework
- **Performance Monitoring**: Page load times and metrics
- **Search Analytics**: Popular search terms and results
- **Google Analytics**: Ready for GA integration
- **Facebook Pixel**: Ready for FB Pixel integration

## 🚀 Performance Features

- **Caching**: Page and query caching
- **Lazy Loading**: Images and content lazy loading
- **Minification**: CSS/JS minification in production
- **Compression**: Gzip compression
- **Image Optimization**: Automatic image resizing
- **Database Optimization**: Indexed queries and connection pooling

## 🐛 Troubleshooting

### Common Issues:

1. **Database Connection Error**
   - Check database credentials in `config/db.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Permission Errors**
   - Check file permissions for uploads/, cache/, logs/
   - Ensure web server has write access

3. **Missing Extensions**
   - Install required PHP extensions (PDO, GD, OpenSSL)
   - Restart web server after installing extensions

4. **404 Errors**
   - Check web server configuration
   - Ensure mod_rewrite is enabled (Apache)
   - Verify file paths are correct

### Debug Mode
Enable debug mode in `config/config.php`:
```php
define('ENVIRONMENT', 'development');
```

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: Check the code comments and this README
- Issues: Report bugs and feature requests

## 🎉 Credits

Built with modern web technologies and best practices:
- PHP 7.4+
- MySQL/MariaDB
- HTML5, CSS3, JavaScript ES6+
- Font Awesome icons
- Modern CSS Grid and Flexbox
- Progressive Web App features

---

**ModernShop** - Your complete e-commerce solution! 🛍️
