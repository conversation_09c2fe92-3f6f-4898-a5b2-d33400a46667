// Advanced JavaScript Features
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedFeatures();
});

function initializeAdvancedFeatures() {
    initializeSearch();
    initializeFilters();
    initializeLazyLoading();
    initializeTooltips();
    initializeModals();
    initializeTabs();
    initializeProgressBars();
    initializeInfiniteScroll();
    initializeKeyboardShortcuts();
    initializePerformanceMonitoring();
}

// Advanced Search with Suggestions
function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchSuggestions = document.querySelector('.search-suggestions');
    
    if (!searchInput || !searchSuggestions) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            hideSearchSuggestions();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetchSearchSuggestions(query);
        }, 300);
    });
    
    searchInput.addEventListener('blur', function() {
        setTimeout(hideSearchSuggestions, 200);
    });
    
    searchInput.addEventListener('focus', function() {
        if (this.value.trim().length >= 2) {
            showSearchSuggestions();
        }
    });
}

function fetchSearchSuggestions(query) {
    // Simulate API call for search suggestions
    const suggestions = [
        { text: query, type: 'search', icon: 'fas fa-search' },
        { text: `${query} in Electronics`, type: 'category', icon: 'fas fa-tag' },
        { text: `${query} under $50`, type: 'filter', icon: 'fas fa-filter' },
        { text: `Popular: ${query}`, type: 'popular', icon: 'fas fa-fire' }
    ];
    
    displaySearchSuggestions(suggestions);
}

function displaySearchSuggestions(suggestions) {
    const container = document.querySelector('.search-suggestions');
    if (!container) return;
    
    container.innerHTML = '';
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'search-suggestion';
        item.innerHTML = `
            <i class="${suggestion.icon} search-suggestion-icon"></i>
            <div class="search-suggestion-text">${suggestion.text}</div>
            <div class="search-suggestion-category">${suggestion.type}</div>
        `;
        
        item.addEventListener('click', function() {
            document.querySelector('.search-input').value = suggestion.text;
            hideSearchSuggestions();
            performSearch(suggestion.text);
        });
        
        container.appendChild(item);
    });
    
    showSearchSuggestions();
}

function showSearchSuggestions() {
    const container = document.querySelector('.search-suggestions');
    if (container) {
        container.classList.add('show');
    }
}

function hideSearchSuggestions() {
    const container = document.querySelector('.search-suggestions');
    if (container) {
        container.classList.remove('show');
    }
}

function performSearch(query) {
    window.location.href = `index.php?search=${encodeURIComponent(query)}`;
}

// Advanced Filters
function initializeFilters() {
    const filterOptions = document.querySelectorAll('.filter-option');
    const priceInputs = document.querySelectorAll('.price-input');
    
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            this.classList.toggle('active');
            applyFilters();
        });
    });
    
    priceInputs.forEach(input => {
        input.addEventListener('change', applyFilters);
    });
}

function applyFilters() {
    const activeFilters = document.querySelectorAll('.filter-option.active');
    const minPrice = document.querySelector('.price-input[name="min_price"]')?.value;
    const maxPrice = document.querySelector('.price-input[name="max_price"]')?.value;
    
    const filters = {
        categories: [],
        brands: [],
        minPrice: minPrice,
        maxPrice: maxPrice
    };
    
    activeFilters.forEach(filter => {
        const type = filter.dataset.type;
        const value = filter.dataset.value;
        
        if (filters[type]) {
            filters[type].push(value);
        }
    });
    
    // Apply filters to products
    filterProducts(filters);
}

function filterProducts(filters) {
    const products = document.querySelectorAll('.product-card');
    
    products.forEach(product => {
        let show = true;
        
        // Category filter
        if (filters.categories.length > 0) {
            const productCategory = product.dataset.category;
            if (!filters.categories.includes(productCategory)) {
                show = false;
            }
        }
        
        // Price filter
        const productPrice = parseFloat(product.dataset.price);
        if (filters.minPrice && productPrice < parseFloat(filters.minPrice)) {
            show = false;
        }
        if (filters.maxPrice && productPrice > parseFloat(filters.maxPrice)) {
            show = false;
        }
        
        // Show/hide product with animation
        if (show) {
            product.style.display = 'block';
            product.style.animation = 'fadeInUp 0.3s ease-out';
        } else {
            product.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                product.style.display = 'none';
            }, 300);
        }
    });
}

// Enhanced Lazy Loading - Skeleton loading removed
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;

                // Load image directly without skeleton
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');

                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px'
    });

    images.forEach(img => imageObserver.observe(img));
}

// Advanced Tooltips
function initializeTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    
    tooltips.forEach(element => {
        const tooltipText = element.dataset.tooltip;
        
        const tooltipElement = document.createElement('div');
        tooltipElement.className = 'tooltip-content';
        tooltipElement.textContent = tooltipText;
        
        element.classList.add('tooltip');
        element.appendChild(tooltipElement);
    });
}

// Advanced Modal System
function initializeModals() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const modals = document.querySelectorAll('.modal-overlay');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.dataset.modal;
            openModal(modalId);
        });
    });
    
    modals.forEach(modal => {
        const closeBtn = modal.querySelector('.modal-close');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => closeModal(modal));
        }
        
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this);
            }
        });
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal-overlay.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
}

function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = '';
}

// Advanced Tabs
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabGroup = this.closest('.tabs');
            const targetId = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabGroup.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            tabGroup.parentElement.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetId).classList.add('active');
        });
    });
}

// Advanced Progress Bars
function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const progressFill = progressBar.querySelector('.progress-fill');
                const targetWidth = progressBar.dataset.progress || '0';
                
                setTimeout(() => {
                    progressFill.style.width = targetWidth + '%';
                }, 100);
                
                progressObserver.unobserve(progressBar);
            }
        });
    });
    
    progressBars.forEach(bar => progressObserver.observe(bar));
}

// Infinite Scroll
function initializeInfiniteScroll() {
    const container = document.querySelector('.products-grid');
    if (!container) return;
    
    let loading = false;
    let page = 1;
    
    const loadMoreObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !loading) {
                loadMoreProducts();
            }
        });
    });
    
    // Create load more trigger element
    const loadTrigger = document.createElement('div');
    loadTrigger.className = 'load-more-trigger';
    loadTrigger.style.height = '20px';
    container.parentElement.appendChild(loadTrigger);
    
    loadMoreObserver.observe(loadTrigger);
    
    function loadMoreProducts() {
        loading = true;
        page++;

        // Show simple loading message
        showLoadingMessage();

        // Simulate API call
        setTimeout(() => {
            // Remove loading message
            removeLoadingMessage();

            // Add new products (simulated)
            addNewProducts();

            loading = false;
        }, 1000);
    }

    function showLoadingMessage() {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading-message';
        loadingDiv.innerHTML = '<p style="text-align: center; padding: 2rem; color: var(--color-gray-600);">Loading more products...</p>';

        container.appendChild(loadingDiv);
    }

    function removeLoadingMessage() {
        const loadingDiv = container.querySelector('.loading-message');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }
    
    function addNewProducts() {
        // This would normally fetch from server
        console.log('Loading more products...');
    }
}

// Keyboard Shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals/dropdowns
        if (e.key === 'Escape') {
            hideSearchSuggestions();
        }
    });
}

// Performance Monitoring
function initializePerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', function() {
        const loadTime = performance.now();
        console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
        
        // Send to analytics (if implemented)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_load_time', {
                value: Math.round(loadTime)
            });
        }
    });
    
    // Monitor largest contentful paint
    if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log(`LCP: ${lastEntry.startTime.toFixed(2)}ms`);
        });
        
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
}

// Utility Functions
const AdvancedUtils = {
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Format currency with locale support
    formatCurrency: function(amount, currency = 'USD', locale = 'en-US') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // Copy to clipboard
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return Promise.resolve();
        }
    },
    
    // Generate unique ID
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
};

// Export for global use
window.AdvancedUtils = AdvancedUtils;
window.openModal = openModal;
window.closeModal = closeModal;
