<?php
/**
 * Simple Order Test Script
 * This creates a test order to verify the order system is working
 */

require_once 'config/db-sqlite.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login
require_login();

echo "<h2>Order System Test</h2>\n";

// Check if we have products
try {
    $stmt = $pdo->query("SELECT * FROM products LIMIT 1");
    $test_product = $stmt->fetch();
    
    if (!$test_product) {
        echo "<div style='color: red;'>❌ No products found in database</div>\n";
        exit;
    }
    
    echo "<div style='color: green;'>✅ Test product found: {$test_product['name']}</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>\n";
    exit;
}

// Create test order
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_order'])) {
    try {
        $pdo->beginTransaction();
        
        // Create a test order
        $stmt = $pdo->prepare("
            INSERT INTO orders (user_id, product_id, quantity, total_price) 
            VALUES (?, ?, ?, ?)
        ");
        
        $test_quantity = 2;
        $test_total = $test_product['price'] * $test_quantity;
        
        $stmt->execute([
            $_SESSION['user_id'],
            $test_product['id'],
            $test_quantity,
            $test_total
        ]);
        
        $order_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        echo "<div style='color: green;'>✅ Test order created successfully!</div>\n";
        echo "<p><strong>Order ID:</strong> $order_id</p>\n";
        echo "<p><strong>Product:</strong> {$test_product['name']}</p>\n";
        echo "<p><strong>Quantity:</strong> $test_quantity</p>\n";
        echo "<p><strong>Total:</strong> $" . number_format($test_total, 2) . "</p>\n";
        echo "<p><a href='orders.php'>View your orders</a></p>\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<div style='color: red;'>❌ Error creating test order: " . $e->getMessage() . "</div>\n";
    }
}

// Check existing orders
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.name as product_name 
        FROM orders o 
        JOIN products p ON o.product_id = p.id 
        WHERE o.user_id = ? 
        ORDER BY o.order_date DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $existing_orders = $stmt->fetchAll();
    
    echo "<h3>Your Recent Orders:</h3>\n";
    if (empty($existing_orders)) {
        echo "<p>No orders found.</p>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>Order ID</th><th>Product</th><th>Quantity</th><th>Total</th><th>Date</th></tr>\n";
        foreach ($existing_orders as $order) {
            echo "<tr>\n";
            echo "<td>{$order['id']}</td>\n";
            echo "<td>{$order['product_name']}</td>\n";
            echo "<td>{$order['quantity']}</td>\n";
            echo "<td>$" . number_format($order['total_price'], 2) . "</td>\n";
            echo "<td>{$order['order_date']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error loading orders: " . $e->getMessage() . "</div>\n";
}

if (!isset($_POST['create_test_order'])) {
?>
<br>
<form method="POST">
    <button type="submit" name="create_test_order" value="1" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
        Create Test Order
    </button>
</form>

<p><a href="index.php">← Back to Store</a></p>
<p><a href="orders.php">← View Orders</a></p>
<?php
}
?>
