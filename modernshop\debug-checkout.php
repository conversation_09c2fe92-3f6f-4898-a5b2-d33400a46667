<?php
/**
 * Debug Checkout Script
 * This script helps debug the checkout process
 */

require_once 'config/db-sqlite.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Require login for checkout
require_login();

echo "<h2>Debug Checkout Process</h2>\n";

// Check if cart exists
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    echo "<div style='color: red;'>❌ Cart is empty</div>\n";
    echo "<p><a href='index.php'>Go back to store</a></p>\n";
    exit;
}

echo "<div style='color: green;'>✅ Cart exists with " . count($_SESSION['cart']) . " items</div>\n";

// Check cart contents
echo "<h3>Cart Contents:</h3>\n";
foreach ($_SESSION['cart'] as $product_id => $item) {
    echo "<p>Product ID: $product_id, Quantity: {$item['quantity']}</p>\n";
}

// Test database connection
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $count = $stmt->fetch()['count'];
    echo "<div style='color: green;'>✅ Database connection working. Products count: $count</div>\n";
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>\n";
}

// Test cart item retrieval
echo "<h3>Testing Cart Item Retrieval:</h3>\n";
try {
    $product_ids = array_keys($_SESSION['cart']);
    $placeholders = str_repeat('?,', count($product_ids) - 1) . '?';
    
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
    $stmt->execute($product_ids);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='color: green;'>✅ Retrieved " . count($products) . " products from database</div>\n";
    
    $cart_items = [];
    $cart_total = 0;
    
    foreach ($products as $product) {
        $cart_item = $_SESSION['cart'][$product['id']];
        $item_total = $product['price'] * $cart_item['quantity'];
        
        $cart_items[] = [
            'product' => $product,
            'quantity' => $cart_item['quantity'],
            'item_total' => $item_total
        ];
        
        $cart_total += $item_total;
        
        echo "<p>Product: {$product['name']}, Price: {$product['price']}, Qty: {$cart_item['quantity']}, Total: $item_total</p>\n";
    }
    
    echo "<div style='color: green;'>✅ Cart total: $cart_total</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error retrieving cart items: " . $e->getMessage() . "</div>\n";
}

// Test order creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_order'])) {
    echo "<h3>Testing Order Creation:</h3>\n";
    
    try {
        $pdo->beginTransaction();
        
        $order_ids = [];
        foreach ($cart_items as $item) {
            $stmt = $pdo->prepare("
                INSERT INTO orders (user_id, product_id, quantity, total_price) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                $item['product']['id'],
                $item['quantity'],
                $item['item_total']
            ]);
            
            $order_id = $pdo->lastInsertId();
            $order_ids[] = $order_id;
            
            echo "<p>✅ Created order ID: $order_id for product: {$item['product']['name']}</p>\n";
            
            // Update product stock
            $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
            $stmt->execute([$item['quantity'], $item['product']['id']]);
            
            echo "<p>✅ Updated stock for product: {$item['product']['name']}</p>\n";
        }
        
        $pdo->commit();
        
        echo "<div style='color: green;'>✅ Order creation successful! Order IDs: " . implode(', ', $order_ids) . "</div>\n";
        
        // Test clearing cart
        clear_cart();
        echo "<div style='color: green;'>✅ Cart cleared</div>\n";
        
        echo "<p><a href='orders.php'>View your orders</a></p>\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<div style='color: red;'>❌ Order creation failed: " . $e->getMessage() . "</div>\n";
        echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    }
}

if (!isset($_POST['test_order'])) {
?>
<form method="POST">
    <button type="submit" name="test_order" value="1" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
        Test Order Creation
    </button>
</form>

<p><a href="checkout.php">← Back to Checkout</a></p>
<p><a href="cart.php">← Back to Cart</a></p>
<?php
}
?>
