<?php
// SQLite Database Configuration
$database_file = 'database/modernshop.db';

// Create database directory if it doesn't exist
if (!is_dir('database')) {
    mkdir('database', 0755, true);
}

try {
    $pdo = new PDO("sqlite:$database_file");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // Enable foreign key constraints for SQLite
    $pdo->exec('PRAGMA foreign_keys = ON');
    
} catch(PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}
?>
