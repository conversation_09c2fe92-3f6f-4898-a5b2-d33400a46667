<?php
$page_title = "Shopping Cart";
$page_description = "Review your selected items and proceed to checkout";

require_once 'config/db.php';
require_once 'includes/functions.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    $response = ['success' => false, 'message' => ''];
    
    switch ($action) {
        case 'add':
            $product_id = (int)($_POST['product_id'] ?? 0);
            $quantity = (int)($_POST['quantity'] ?? 1);
            
            if ($product_id > 0 && $quantity > 0) {
                add_to_cart($product_id, $quantity);
                $response = [
                    'success' => true, 
                    'message' => 'Product added to cart',
                    'cart_count' => get_cart_count()
                ];
            } else {
                $response['message'] = 'Invalid product or quantity';
            }
            break;
            
        case 'update':
            $product_id = (int)($_POST['product_id'] ?? 0);
            $quantity = (int)($_POST['quantity'] ?? 0);
            
            if ($product_id > 0) {
                update_cart_quantity($product_id, $quantity);
                $response = [
                    'success' => true, 
                    'message' => 'Cart updated',
                    'cart_count' => get_cart_count(),
                    'cart_total' => get_cart_total($pdo)
                ];
            } else {
                $response['message'] = 'Invalid product';
            }
            break;
            
        case 'remove':
            $product_id = (int)($_POST['product_id'] ?? 0);
            
            if ($product_id > 0) {
                remove_from_cart($product_id);
                $response = [
                    'success' => true, 
                    'message' => 'Product removed from cart',
                    'cart_count' => get_cart_count(),
                    'cart_total' => get_cart_total($pdo)
                ];
            } else {
                $response['message'] = 'Invalid product';
            }
            break;
            
        case 'clear':
            clear_cart();
            $response = [
                'success' => true, 
                'message' => 'Cart cleared',
                'cart_count' => 0,
                'cart_total' => 0
            ];
            break;
    }
    
    echo json_encode($response);
    exit;
}

// Get cart items with product details
$cart_items = [];
$cart_total = 0;

if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    $product_ids = array_keys($_SESSION['cart']);
    $placeholders = str_repeat('?,', count($product_ids) - 1) . '?';
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
        $stmt->execute($product_ids);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($products as $product) {
            $cart_item = $_SESSION['cart'][$product['id']];
            $item_total = $product['price'] * $cart_item['quantity'];
            
            $cart_items[] = [
                'product' => $product,
                'quantity' => $cart_item['quantity'],
                'item_total' => $item_total
            ];
            
            $cart_total += $item_total;
        }
    } catch (Exception $e) {
        set_flash_message('Error loading cart items.', 'error');
    }
}

include 'includes/header.php';
?>

<div class="container animate-fade-in-up">
    <!-- Page Header -->
    <div class="section-header">
        <h1 class="section-title">
            <i class="fas fa-shopping-cart"></i>
            Shopping Cart
        </h1>
        <p class="section-subtitle">
            <?php if (empty($cart_items)): ?>
                Your cart is empty
            <?php else: ?>
                Review your items and proceed to checkout
            <?php endif; ?>
        </p>
    </div>
    
    <?php if (empty($cart_items)): ?>
        <!-- Empty Cart -->
        <div class="text-center animate-fade-in" style="padding: var(--space-20) 0;">
            <div style="font-size: var(--font-size-5xl); color: var(--color-gray-400); margin-bottom: var(--space-6);">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 style="color: var(--color-gray-600); margin-bottom: var(--space-4);">Your cart is empty</h3>
            <p style="color: var(--color-gray-500); margin-bottom: var(--space-8);">
                Looks like you haven't added any items to your cart yet.
            </p>
            <a href="index.php" class="btn btn-primary btn-lg hover-lift">
                <i class="fas fa-arrow-left"></i>
                Continue Shopping
            </a>
        </div>
    <?php else: ?>
        <!-- Cart Items -->
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Cart Items List -->
            <div class="lg:col-span-2">
                <div class="card animate-fade-in-left">
                    <div class="card-header">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--space-2);">
                            <i class="fas fa-list"></i>
                            Cart Items (<?php echo count($cart_items); ?>)
                        </h3>
                    </div>
                    
                    <div class="card-body" style="padding: 0;">
                        <?php foreach ($cart_items as $index => $item): ?>
                            <div class="cart-item" data-product-id="<?php echo $item['product']['id']; ?>" 
                                 style="padding: var(--space-6); border-bottom: 1px solid var(--color-gray-200); transition: all var(--transition-normal);">
                                
                                <div class="flex gap-4">
                                    <!-- Product Image -->
                                    <div class="flex-shrink-0">
                                        <img 
                                            src="<?php echo get_product_image($item['product']['image']); ?>" 
                                            alt="<?php echo htmlspecialchars($item['product']['name']); ?>"
                                            style="width: 80px; height: 80px; object-fit: cover; border-radius: var(--radius-md);"
                                        >
                                    </div>
                                    
                                    <!-- Product Details -->
                                    <div class="flex-1">
                                        <h4 style="font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-2);">
                                            <a href="product.php?id=<?php echo $item['product']['id']; ?>" class="text-primary hover:text-primary-dark">
                                                <?php echo htmlspecialchars($item['product']['name']); ?>
                                            </a>
                                        </h4>
                                        
                                        <p style="color: var(--color-gray-600); font-size: var(--font-size-sm); margin-bottom: var(--space-3);">
                                            <?php echo htmlspecialchars(truncate_text($item['product']['description'], 80)); ?>
                                        </p>
                                        
                                        <div class="flex items-center justify-between">
                                            <!-- Quantity Controls -->
                                            <div class="flex items-center gap-3">
                                                <span style="font-size: var(--font-size-sm); color: var(--color-gray-600);">Qty:</span>
                                                <div class="flex items-center border border-gray-300 rounded-md overflow-hidden">
                                                    <button class="quantity-btn" data-action="decrease" data-product-id="<?php echo $item['product']['id']; ?>"
                                                            style="padding: var(--space-2) var(--space-3); background: var(--color-gray-100); border: none; cursor: pointer;">
                                                        <i class="fas fa-minus"></i>
                                                    </button>
                                                    <input type="number" class="quantity-input" value="<?php echo $item['quantity']; ?>" 
                                                           min="1" max="<?php echo $item['product']['stock']; ?>"
                                                           data-product-id="<?php echo $item['product']['id']; ?>"
                                                           style="width: 50px; text-align: center; border: none; padding: var(--space-2);">
                                                    <button class="quantity-btn" data-action="increase" data-product-id="<?php echo $item['product']['id']; ?>"
                                                            style="padding: var(--space-2) var(--space-3); background: var(--color-gray-100); border: none; cursor: pointer;">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <!-- Price and Remove -->
                                            <div class="flex items-center gap-4">
                                                <div class="text-right">
                                                    <div style="font-size: var(--font-size-sm); color: var(--color-gray-500);">
                                                        <?php echo format_price($item['product']['price']); ?> each
                                                    </div>
                                                    <div class="item-total" style="font-size: var(--font-size-lg); font-weight: var(--font-weight-bold); color: var(--color-primary);">
                                                        <?php echo format_price($item['item_total']); ?>
                                                    </div>
                                                </div>
                                                
                                                <button class="remove-item btn btn-secondary btn-sm" data-product-id="<?php echo $item['product']['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Cart Actions -->
                    <div class="card-footer">
                        <div class="flex justify-between items-center">
                            <button id="clear-cart" class="btn btn-secondary">
                                <i class="fas fa-trash-alt"></i>
                                Clear Cart
                            </button>
                            
                            <a href="index.php" class="btn btn-outline">
                                <i class="fas fa-arrow-left"></i>
                                Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="card animate-fade-in-right" style="position: sticky; top: var(--space-20);">
                    <div class="card-header">
                        <h3 style="margin: 0;">
                            <i class="fas fa-receipt"></i>
                            Order Summary
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <!-- Subtotal -->
                        <div class="flex justify-between items-center" style="margin-bottom: var(--space-4); padding-bottom: var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
                            <span>Subtotal (<?php echo count($cart_items); ?> items)</span>
                            <span id="cart-subtotal" style="font-weight: var(--font-weight-semibold);">
                                <?php echo format_price($cart_total); ?>
                            </span>
                        </div>
                        
                        <!-- Shipping -->
                        <div class="flex justify-between items-center" style="margin-bottom: var(--space-4);">
                            <span>Shipping</span>
                            <span style="color: var(--color-success); font-weight: var(--font-weight-medium);">
                                FREE
                            </span>
                        </div>
                        
                        <!-- Tax -->
                        <div class="flex justify-between items-center" style="margin-bottom: var(--space-6); padding-bottom: var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
                            <span>Tax</span>
                            <span>$0.00</span>
                        </div>
                        
                        <!-- Total -->
                        <div class="flex justify-between items-center" style="margin-bottom: var(--space-6); font-size: var(--font-size-xl); font-weight: var(--font-weight-bold);">
                            <span>Total</span>
                            <span id="cart-total" style="color: var(--color-primary);">
                                <?php echo format_price($cart_total); ?>
                            </span>
                        </div>
                        
                        <!-- Checkout Button -->
                        <?php if (is_logged_in()): ?>
                            <a href="checkout.php" class="btn btn-primary w-full btn-lg hover-lift">
                                <i class="fas fa-credit-card"></i>
                                Proceed to Checkout
                            </a>
                        <?php else: ?>
                            <div class="alert alert-info" style="margin-bottom: var(--space-4);">
                                <i class="fas fa-info-circle"></i>
                                Please log in to proceed with checkout
                            </div>
                            <a href="login.php" class="btn btn-primary w-full btn-lg">
                                <i class="fas fa-sign-in-alt"></i>
                                Login to Checkout
                            </a>
                        <?php endif; ?>
                        
                        <!-- Security Badge -->
                        <div style="text-align: center; margin-top: var(--space-6); padding-top: var(--space-4); border-top: 1px solid var(--color-gray-200);">
                            <div style="color: var(--color-gray-500); font-size: var(--font-size-sm);">
                                <i class="fas fa-shield-alt" style="color: var(--color-success);"></i>
                                Secure Checkout
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            const productId = this.dataset.productId;
            const input = document.querySelector(`.quantity-input[data-product-id="${productId}"]`);
            
            let quantity = parseInt(input.value);
            const max = parseInt(input.max);
            
            if (action === 'increase' && quantity < max) {
                quantity++;
            } else if (action === 'decrease' && quantity > 1) {
                quantity--;
            }
            
            input.value = quantity;
            updateCartItem(productId, quantity);
        });
    });
    
    // Quantity input change
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.dataset.productId;
            let quantity = parseInt(this.value);
            const max = parseInt(this.max);
            
            if (quantity < 1) quantity = 1;
            if (quantity > max) quantity = max;
            
            this.value = quantity;
            updateCartItem(productId, quantity);
        });
    });
    
    // Remove item buttons
    document.querySelectorAll('.remove-item').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.dataset.productId;
            removeCartItem(productId);
        });
    });
    
    // Clear cart button
    const clearCartBtn = document.getElementById('clear-cart');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear your cart?')) {
                clearCart();
            }
        });
    }
});

// Update cart item quantity
function updateCartItem(productId, quantity) {
    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=update&product_id=${productId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartDisplay(data);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Error updating cart', 'error');
    });
}

// Remove cart item
function removeCartItem(productId) {
    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=remove&product_id=${productId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove item from DOM with animation
            const cartItem = document.querySelector(`.cart-item[data-product-id="${productId}"]`);
            if (cartItem) {
                cartItem.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    cartItem.remove();
                    
                    // Check if cart is empty
                    const remainingItems = document.querySelectorAll('.cart-item');
                    if (remainingItems.length === 0) {
                        location.reload();
                    }
                }, 300);
            }
            
            updateCartDisplay(data);
            showNotification('Item removed from cart', 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Error removing item', 'error');
    });
}

// Clear entire cart
function clearCart() {
    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=clear'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Error clearing cart', 'error');
    });
}

// Update cart display
function updateCartDisplay(data) {
    // Update cart count in header
    const cartCount = document.querySelector('.cart-count');
    if (cartCount) {
        if (data.cart_count > 0) {
            cartCount.textContent = data.cart_count;
            cartCount.style.display = 'block';
        } else {
            cartCount.style.display = 'none';
        }
    }
    
    // Update totals
    const subtotalElement = document.getElementById('cart-subtotal');
    const totalElement = document.getElementById('cart-total');
    
    if (subtotalElement && data.cart_total !== undefined) {
        subtotalElement.textContent = Utils.formatCurrency(data.cart_total);
    }
    
    if (totalElement && data.cart_total !== undefined) {
        totalElement.textContent = Utils.formatCurrency(data.cart_total);
    }
}

// Add fadeOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(-20px);
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
