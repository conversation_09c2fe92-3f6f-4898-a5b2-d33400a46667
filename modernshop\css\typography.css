/* Typography System */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Base Typography */
html {
    font-size: 16px;
    font-family: var(--font-family-primary);
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--color-gray-900);
    background-color: var(--color-white);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

h1 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-extrabold);
    letter-spacing: -0.025em;
}

h2 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.025em;
}

h3 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

h4 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

h6 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

/* Responsive Typography */
@media (max-width: 768px) {
    h1 {
        font-size: var(--font-size-4xl);
    }
    
    h2 {
        font-size: var(--font-size-3xl);
    }
    
    h3 {
        font-size: var(--font-size-2xl);
    }
}

/* Paragraphs */
p {
    margin-bottom: var(--space-4);
    color: var(--color-gray-700);
    line-height: var(--line-height-relaxed);
}

/* Links */
a {
    color: var(--color-primary);
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

/* Text Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Color Utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }
.text-gray-900 { color: var(--color-gray-900); }
.text-white { color: var(--color-white); }

/* Special Typography */
.lead {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
    color: var(--color-gray-600);
}

.small {
    font-size: var(--font-size-sm);
    color: var(--color-gray-500);
}

.mono {
    font-family: var(--font-family-mono);
}

/* Text truncation */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
