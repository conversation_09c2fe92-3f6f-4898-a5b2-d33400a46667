-- SQLite Schema for ModernShop
-- SQLite-compatible version of the database schema

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name TEXT NOT NULL,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    stock INTEGER DEFAULT 0,
    category_id INTEGER,
    image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identifier TEXT NOT NULL,
    action TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    user_id INTEGER,
    event TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'general',
    data TEXT, -- JSON as TEXT in SQLite
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- A/B testing tables
CREATE TABLE IF NOT EXISTS ab_tests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    variants TEXT NOT NULL, -- JSON as TEXT
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed')),
    start_date DATETIME,
    end_date DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ab_test_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_name TEXT NOT NULL,
    variant TEXT NOT NULL,
    identifier TEXT NOT NULL,
    converted INTEGER DEFAULT 0, -- BOOLEAN as INTEGER
    conversion_date DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (test_name) REFERENCES ab_tests(name) ON DELETE CASCADE,
    UNIQUE(test_name, identifier)
);

-- Product reviews table
CREATE TABLE IF NOT EXISTS product_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title TEXT,
    review TEXT,
    verified_purchase INTEGER DEFAULT 0, -- BOOLEAN as INTEGER
    helpful_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, product_id)
);

-- Wishlist table
CREATE TABLE IF NOT EXISTS wishlists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE(user_id, product_id)
);

-- System settings
CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'boolean', 'json')),
    description TEXT,
    is_public INTEGER DEFAULT 0, -- BOOLEAN as INTEGER
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample categories
INSERT OR IGNORE INTO categories (id, name) VALUES 
(1, 'Electronics'),
(2, 'Clothing'),
(3, 'Home & Garden'),
(4, 'Sports & Outdoors'),
(5, 'Books');

-- Insert sample products
INSERT OR IGNORE INTO products (id, name, description, price, stock, category_id, image) VALUES 
(1, 'Wireless Headphones', 'High-quality wireless headphones with noise cancellation', 99.99, 50, 1, 'headphones.jpg'),
(2, 'Smartphone', 'Latest smartphone with advanced camera features', 699.99, 30, 1, 'smartphone.jpg'),
(3, 'Laptop', 'Powerful laptop for work and gaming', 1299.99, 20, 1, 'laptop.jpg'),
(4, 'T-Shirt', 'Comfortable cotton t-shirt', 19.99, 100, 2, 'tshirt.jpg'),
(5, 'Jeans', 'Classic blue jeans', 49.99, 75, 2, 'jeans.jpg'),
(6, 'Coffee Maker', 'Automatic coffee maker with timer', 79.99, 25, 3, 'coffee-maker.jpg'),
(7, 'Plant Pot', 'Decorative ceramic plant pot', 24.99, 40, 3, 'plant-pot.jpg'),
(8, 'Running Shoes', 'Lightweight running shoes', 89.99, 60, 4, 'running-shoes.jpg'),
(9, 'Yoga Mat', 'Non-slip yoga mat', 29.99, 35, 4, 'yoga-mat.jpg'),
(10, 'Programming Book', 'Learn modern web development', 39.99, 15, 5, 'programming-book.jpg');

-- Insert default system settings
INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'ModernShop', 'string', 'Website name', 1),
('site_description', 'Your modern e-commerce solution', 'string', 'Website description', 1),
('currency', 'USD', 'string', 'Default currency', 1),
('currency_symbol', '$', 'string', 'Currency symbol', 1),
('tax_rate', '0.08', 'string', 'Default tax rate', 0),
('shipping_cost', '9.99', 'string', 'Default shipping cost', 1),
('free_shipping_threshold', '50.00', 'string', 'Free shipping minimum amount', 1),
('products_per_page', '12', 'integer', 'Products per page', 1),
('enable_reviews', '1', 'boolean', 'Enable product reviews', 1),
('enable_wishlist', '1', 'boolean', 'Enable wishlist feature', 1),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 0);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category_price ON products(category_id, price);
CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);
CREATE INDEX IF NOT EXISTS idx_orders_user_date ON orders(user_id, order_date);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON rate_limits(identifier, action);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event ON analytics_events(event);
