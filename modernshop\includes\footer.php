        </main>
        
        <footer class="site-footer">
            <div class="container">
                <div class="footer-content">
                    <!-- Company Info -->
                    <div class="footer-section">
                        <h3>
                            <i class="fas fa-store"></i>
                            ModernShop
                        </h3>
                        <p>Your trusted online shopping destination. We offer high-quality products at competitive prices with exceptional customer service.</p>
                        <div class="social-links" style="margin-top: var(--space-4);">
                            <a href="#" style="margin-right: var(--space-3); font-size: var(--font-size-lg);">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" style="margin-right: var(--space-3); font-size: var(--font-size-lg);">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" style="margin-right: var(--space-3); font-size: var(--font-size-lg);">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" style="font-size: var(--font-size-lg);">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Quick Links -->
                    <div class="footer-section">
                        <h3>Quick Links</h3>
                        <div style="display: flex; flex-direction: column; gap: var(--space-2);">
                            <a href="index.php">Home</a>
                            <a href="index.php#products">Products</a>
                            <?php if (is_logged_in()): ?>
                                <a href="orders.php">My Orders</a>
                                <a href="profile.php">My Profile</a>
                            <?php else: ?>
                                <a href="login.php">Login</a>
                                <a href="register.php">Register</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Categories -->
                    <div class="footer-section">
                        <h3>Categories</h3>
                        <div style="display: flex; flex-direction: column; gap: var(--space-2);">
                            <?php
                            try {
                                $stmt = $pdo->query("SELECT * FROM categories LIMIT 5");
                                while ($category = $stmt->fetch()):
                            ?>
                                <a href="index.php?category=<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                            <?php 
                                endwhile;
                            } catch (Exception $e) {
                                // Fallback categories if database is not available
                                echo '<a href="#">Electronics</a>';
                                echo '<a href="#">Clothing</a>';
                                echo '<a href="#">Books</a>';
                                echo '<a href="#">Home & Garden</a>';
                                echo '<a href="#">Sports</a>';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="footer-section">
                        <h3>Contact Us</h3>
                        <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                            <div>
                                <i class="fas fa-map-marker-alt" style="margin-right: var(--space-2); color: var(--color-primary);"></i>
                                123 Commerce Street<br>
                                <span style="margin-left: var(--space-5);">Business City, BC 12345</span>
                            </div>
                            <div>
                                <i class="fas fa-phone" style="margin-right: var(--space-2); color: var(--color-primary);"></i>
                                <a href="tel:+1234567890">(*************</a>
                            </div>
                            <div>
                                <i class="fas fa-envelope" style="margin-right: var(--space-2); color: var(--color-primary);"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div>
                                <i class="fas fa-clock" style="margin-right: var(--space-2); color: var(--color-primary);"></i>
                                Mon - Fri: 9:00 AM - 6:00 PM
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="footer-bottom">
                    <p>&copy; <?php echo date('Y'); ?> ModernShop. All rights reserved. | 
                       <a href="#" style="color: var(--color-gray-400);">Privacy Policy</a> | 
                       <a href="#" style="color: var(--color-gray-400);">Terms of Service</a>
                    </p>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Core JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/advanced.js"></script>

    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript for page-specific functionality -->
    <?php if (isset($inline_js)): ?>
        <script>
            <?php echo $inline_js; ?>
        </script>
    <?php endif; ?>
</body>
</html>
