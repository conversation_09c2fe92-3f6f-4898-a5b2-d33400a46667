<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Enable Remote Access to phpMyAdmin</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_access-phpmyadmin-remotely">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/access-phpmyadmin-remotely.pdf">          Download PDF
          <span>access-phpmyadmin-remotely.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Enable Remote Access to phpMyAdmin</h1>
<div class="paragraph">
<p>XAMPP includes <a href="http://www.phpmyadmin.net/">phpMyAdmin</a>, an open-source, browser-based tool for managing MySQL/MariaDB database servers. By default, for security reasons, phpMyAdmin is configured to only accept connections from the host on which XAMPP is installed. Attempting to access phpMyAdmin from any other host will produce the error below:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/access-phpmyadmin-remotely/image1.png" alt="image1">
</div>
</div>
<div class="paragraph">
<p>To enable remote access to phpMyAdmin from other hosts, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Edit the <em>apache/conf/extra/httpd-xampp.conf</em> file in your XAMPP installation directory (usually, <em>C:\xampp</em>).</p>
</li>
<li>
<p>Within this file, find the block below:</p>
<div class="literalblock">
<div class="content">
<pre>&lt;Directory "/xampp/phpMyAdmin"&gt;
  AllowOverride AuthConfig
  Require local
  ...</pre>
</div>
</div>
<div class="paragraph">
<p>Update this block and replace <em>Require local</em> with <em>Require all granted</em>, so that it looks like this:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>&lt;Directory "/xampp/phpMyAdmin"&gt;
  AllowOverride AuthConfig
  Require all granted
  ...</pre>
</div>
</div>
</li>
<li>
<p>Save the file and restart the Apache server using the XAMPP control panel.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>You should now be able to access phpMyAdmin from other hosts.</p>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
