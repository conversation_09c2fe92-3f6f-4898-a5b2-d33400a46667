<?php
// Application Configuration

// Environment settings
define('ENVIRONMENT', 'development'); // development, staging, production

// Site settings
define('SITE_NAME', 'ModernShop');
define('SITE_URL', 'http://localhost/modernshop');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+****************');

// Database settings (already in db.php, but centralized here)
define('DB_HOST', 'localhost');
define('DB_NAME', 'modernshop');
define('DB_USER', 'root');
define('DB_PASS', '');

// Security settings
define('CSRF_TOKEN_EXPIRE', 3600); // 1 hour
define('SESSION_LIFETIME', 86400); // 24 hours
define('PASSWORD_MIN_LENGTH', 8);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('UPLOAD_MAX_SIZE', 5242880); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', 'uploads/');

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Payment settings
define('STRIPE_PUBLISHABLE_KEY', '');
define('STRIPE_SECRET_KEY', '');
define('PAYPAL_CLIENT_ID', '');
define('PAYPAL_CLIENT_SECRET', '');

// Analytics settings
define('GOOGLE_ANALYTICS_ID', '');
define('FACEBOOK_PIXEL_ID', '');

// Cache settings
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour
define('CACHE_PATH', 'cache/');

// Performance settings
define('GZIP_ENABLED', true);
define('MINIFY_CSS', ENVIRONMENT === 'production');
define('MINIFY_JS', ENVIRONMENT === 'production');
define('LAZY_LOADING', true);

// Feature flags
define('FEATURES', [
    'reviews' => true,
    'wishlist' => true,
    'compare' => true,
    'social_login' => false,
    'multi_currency' => false,
    'inventory_tracking' => true,
    'advanced_search' => true,
    'ab_testing' => true
]);

// Pagination settings
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);
define('CUSTOMERS_PER_PAGE', 20);

// Image settings
define('IMAGE_QUALITY', 85);
define('IMAGE_MAX_WIDTH', 1200);
define('IMAGE_MAX_HEIGHT', 1200);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 300);

// Currency settings
define('DEFAULT_CURRENCY', 'USD');
define('CURRENCY_SYMBOL', '$');
define('CURRENCY_POSITION', 'before'); // before or after

// Timezone
define('DEFAULT_TIMEZONE', 'America/New_York');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Error reporting based on environment
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/error.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/error.log');
}

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', ENVIRONMENT === 'production' ? 1 : 0);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Create necessary directories
$directories = [
    'logs',
    'cache',
    'uploads',
    'uploads/products',
    'uploads/users'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Autoloader for classes
spl_autoload_register(function ($class) {
    $paths = [
        'includes/classes/',
        'includes/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            break;
        }
    }
});

// Global error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line,
        'timestamp' => date('Y-m-d H:i:s'),
        'url' => $_SERVER['REQUEST_URI'] ?? '',
        'user_id' => $_SESSION['user_id'] ?? null
    ];
    
    error_log("ERROR: " . json_encode($error));
    
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #ff6b6b; color: white; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>Error:</strong> {$message} in {$file} on line {$line}";
        echo "</div>";
    }
    
    return true;
});

// Global exception handler
set_exception_handler(function($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'timestamp' => date('Y-m-d H:i:s'),
        'url' => $_SERVER['REQUEST_URI'] ?? '',
        'user_id' => $_SESSION['user_id'] ?? null
    ];
    
    error_log("EXCEPTION: " . json_encode($error));
    
    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #ff6b6b; color: white; padding: 15px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>File:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine() . "<br>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        http_response_code(500);
        include 'error-pages/500.php';
    }
});

// Utility functions
function config($key, $default = null) {
    $config = [
        'site.name' => SITE_NAME,
        'site.url' => SITE_URL,
        'site.email' => SITE_EMAIL,
        'site.phone' => SITE_PHONE,
        'cache.enabled' => CACHE_ENABLED,
        'cache.lifetime' => CACHE_LIFETIME,
        'features' => FEATURES,
        'currency.symbol' => CURRENCY_SYMBOL,
        'currency.position' => CURRENCY_POSITION
    ];
    
    return $config[$key] ?? $default;
}

function is_feature_enabled($feature) {
    return FEATURES[$feature] ?? false;
}

function get_upload_path($type = '') {
    $path = UPLOAD_PATH;
    if ($type) {
        $path .= $type . '/';
    }
    return $path;
}

function log_activity($action, $details = []) {
    $log = [
        'action' => $action,
        'details' => $details,
        'user_id' => $_SESSION['user_id'] ?? null,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    error_log("ACTIVITY: " . json_encode($log));
}

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set default timezone
date_default_timezone_set(DEFAULT_TIMEZONE);
?>
