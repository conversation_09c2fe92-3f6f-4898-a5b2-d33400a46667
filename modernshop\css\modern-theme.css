/* Modern Theme CSS - Consolidated for cache-busting */

/* CSS Custom Properties - Modern Aesthetic Light Theme */
:root {
    /* Colors - Modern Aesthetic Light Theme */
    --color-primary: #6366f1;
    --color-primary-light: #818cf8;
    --color-primary-dark: #4f46e5;
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-500: #6366f1;
    --color-primary-600: #4f46e5;
    --color-primary-700: #4338ca;
    
    --color-secondary: #64748b;
    --color-secondary-light: #94a3b8;
    --color-secondary-dark: #475569;
    
    --color-success: #22c55e;
    --color-success-light: #4ade80;
    --color-success-dark: #16a34a;
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    
    --color-warning: #f59e0b;
    --color-warning-light: #fbbf24;
    --color-warning-dark: #d97706;
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    
    --color-error: #ef4444;
    --color-error-light: #f87171;
    --color-error-dark: #dc2626;
    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;

    /* Neutral Colors - Soft & Modern */
    --color-white: #ffffff;
    --color-gray-50: #fafafa;
    --color-gray-100: #f4f4f5;
    --color-gray-200: #e4e4e7;
    --color-gray-300: #d4d4d8;
    --color-gray-400: #a1a1aa;
    --color-gray-500: #71717a;
    --color-gray-600: #52525b;
    --color-gray-700: #3f3f46;
    --color-gray-800: #27272a;
    --color-gray-900: #18181b;
    --color-black: #09090b;
    
    /* Accent Colors - Modern Palette */
    --color-accent-purple: #a855f7;
    --color-accent-pink: #ec4899;
    --color-accent-rose: #f43f5e;
    --color-accent-orange: #f97316;
    --color-accent-amber: #f59e0b;
    --color-accent-emerald: #10b981;
    --color-accent-teal: #14b8a6;
    --color-accent-cyan: #06b6d4;
    --color-accent-sky: #0ea5e9;
    
    /* Background Colors */
    --color-background: #ffffff;
    --color-background-secondary: #fafafa;
    --color-background-tertiary: #f4f4f5;
    --color-surface: #ffffff;
    --color-surface-hover: #f9fafb;
}

/* Global Overrides for Modern Theme */
body {
    background-color: var(--color-background-secondary) !important;
}

.hero {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent-purple) 50%, var(--color-accent-pink) 100%) !important;
}

.logo {
    color: var(--color-primary) !important;
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-primary) !important;
}

.nav-link.active::after {
    background-color: var(--color-primary) !important;
}

.cart-icon:hover {
    color: var(--color-primary) !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
    border-color: var(--color-primary) !important;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-accent-purple) 100%) !important;
    border-color: var(--color-primary-dark) !important;
}

.product-card {
    background-color: var(--color-surface) !important;
}

.product-card:hover {
    border-color: var(--color-primary) !important;
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
}

.product-card-price {
    color: var(--color-primary) !important;
}

.card {
    background-color: var(--color-surface) !important;
}

.card:hover {
    border-color: var(--color-primary) !important;
}

.section-title {
    color: var(--color-gray-900) !important;
}

.section-subtitle {
    color: var(--color-gray-600) !important;
}

/* Remove any skeleton loading animations */
.skeleton,
.skeleton-text,
.skeleton-card,
.skeleton-image,
.loading-skeleton {
    display: none !important;
}

/* Enhanced modern styling */
.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.product-card:hover::before {
    opacity: 0.05;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Modern focus states */
.form-input:focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* Badge colors */
.badge-primary {
    background-color: var(--color-primary-100) !important;
    color: var(--color-primary-700) !important;
}

/* Alert colors */
.alert-info {
    background-color: var(--color-primary-50) !important;
    border-color: var(--color-primary-200) !important;
    color: var(--color-primary-800) !important;
}

/* Loading spinner colors */
.loading-spinner {
    border-color: var(--color-gray-200) !important;
    border-top-color: var(--color-primary) !important;
}

.spinner {
    border-color: var(--color-gray-200) !important;
    border-top-color: var(--color-primary) !important;
}

/* Glow effects */
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3) !important;
}

.animate-glow {
    box-shadow: 0 0 5px rgba(99, 102, 241, 0.5) !important;
}

/* Loading dots */
.loading-dots span {
    background: var(--color-primary) !important;
}

/* Loading bars */
.loading-bars span {
    background: var(--color-primary) !important;
}
