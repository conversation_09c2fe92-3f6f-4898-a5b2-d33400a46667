/* Modern Theme CSS - Consolidated for cache-busting */

/* CSS Custom Properties - Modern Aesthetic Light Theme */
:root {
    /* Colors - Modern Aesthetic Light Theme */
    --color-primary: #6366f1;
    --color-primary-light: #818cf8;
    --color-primary-dark: #4f46e5;
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-500: #6366f1;
    --color-primary-600: #4f46e5;
    --color-primary-700: #4338ca;
    
    --color-secondary: #64748b;
    --color-secondary-light: #94a3b8;
    --color-secondary-dark: #475569;
    
    --color-success: #22c55e;
    --color-success-light: #4ade80;
    --color-success-dark: #16a34a;
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    
    --color-warning: #f59e0b;
    --color-warning-light: #fbbf24;
    --color-warning-dark: #d97706;
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    
    --color-error: #ef4444;
    --color-error-light: #f87171;
    --color-error-dark: #dc2626;
    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;

    /* Neutral Colors - Soft & Modern */
    --color-white: #ffffff;
    --color-gray-50: #fafafa;
    --color-gray-100: #f4f4f5;
    --color-gray-200: #e4e4e7;
    --color-gray-300: #d4d4d8;
    --color-gray-400: #a1a1aa;
    --color-gray-500: #71717a;
    --color-gray-600: #52525b;
    --color-gray-700: #3f3f46;
    --color-gray-800: #27272a;
    --color-gray-900: #18181b;
    --color-black: #09090b;
    
    /* Accent Colors - Modern Palette */
    --color-accent-purple: #a855f7;
    --color-accent-pink: #ec4899;
    --color-accent-rose: #f43f5e;
    --color-accent-orange: #f97316;
    --color-accent-amber: #f59e0b;
    --color-accent-emerald: #10b981;
    --color-accent-teal: #14b8a6;
    --color-accent-cyan: #06b6d4;
    --color-accent-sky: #0ea5e9;
    
    /* Background Colors */
    --color-background: #ffffff;
    --color-background-secondary: #fafafa;
    --color-background-tertiary: #f4f4f5;
    --color-surface: #ffffff;
    --color-surface-hover: #f9fafb;
}

/* Global Overrides for Modern Theme */
body {
    background-color: var(--color-background-secondary) !important;
}

.hero {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent-purple) 50%, var(--color-accent-pink) 100%) !important;
}

.logo {
    color: var(--color-primary) !important;
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-primary) !important;
}

.nav-link.active::after {
    background-color: var(--color-primary) !important;
}

.cart-icon:hover {
    color: var(--color-primary) !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
    border-color: var(--color-primary) !important;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-accent-purple) 100%) !important;
    border-color: var(--color-primary-dark) !important;
}

.product-card {
    background-color: var(--color-surface) !important;
}

.product-card:hover {
    border-color: var(--color-primary) !important;
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
}

.product-card-price {
    color: var(--color-primary) !important;
}

.card {
    background-color: var(--color-surface) !important;
}

.card:hover {
    border-color: var(--color-primary) !important;
}

.section-title {
    color: var(--color-gray-900) !important;
}

.section-subtitle {
    color: var(--color-gray-600) !important;
}

/* Remove any skeleton loading animations */
.skeleton,
.skeleton-text,
.skeleton-card,
.skeleton-image,
.loading-skeleton {
    display: none !important;
}

/* Enhanced modern styling */
.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.product-card:hover::before {
    opacity: 0.05;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Modern focus states */
.form-input:focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* Badge colors */
.badge-primary {
    background-color: var(--color-primary-100) !important;
    color: var(--color-primary-700) !important;
}

/* Alert colors */
.alert-info {
    background-color: var(--color-primary-50) !important;
    border-color: var(--color-primary-200) !important;
    color: var(--color-primary-800) !important;
}

/* Loading spinner colors */
.loading-spinner {
    border-color: var(--color-gray-200) !important;
    border-top-color: var(--color-primary) !important;
}

.spinner {
    border-color: var(--color-gray-200) !important;
    border-top-color: var(--color-primary) !important;
}

/* Glow effects */
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3) !important;
}

.animate-glow {
    box-shadow: 0 0 5px rgba(99, 102, 241, 0.5) !important;
}

/* Loading dots */
.loading-dots span {
    background: var(--color-primary) !important;
}

/* Loading bars */
.loading-bars span {
    background: var(--color-primary) !important;
}

/* Clean Form Styling - No Floating Labels */
.form-group {
    margin-bottom: 1.5rem !important;
}

.form-label {
    display: block !important;
    position: static !important;
    transform: none !important;
    background: none !important;
    padding: 0 !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: var(--color-gray-700) !important;
    pointer-events: auto !important;
}

.form-input,
.form-select {
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    border: 2px solid var(--color-gray-300) !important;
    border-radius: 0.5rem !important;
    font-size: 1rem !important;
    background: var(--color-white) !important;
    transition: all 0.2s ease !important;
    box-sizing: border-box !important;
}

.form-input:focus,
.form-select:focus {
    outline: none !important;
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

.form-input::placeholder {
    color: var(--color-gray-400) !important;
    opacity: 1 !important;
}

.form-input:focus::placeholder {
    opacity: 0.5 !important;
}

/* Remove any floating label animations */
.form-group.focused .form-label,
.form-group.has-value .form-label {
    transform: none !important;
    color: var(--color-gray-700) !important;
    background: none !important;
    padding: 0 !important;
}

/* Form grid layout */
.form-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1.5rem !important;
}

.form-group.full-width {
    grid-column: 1 / -1 !important;
}

/* Checkout specific styling */
.checkout-form .form-group {
    margin-bottom: 1.5rem !important;
}

.checkout-form .form-label {
    font-weight: 600 !important;
    color: var(--color-gray-700) !important;
    margin-bottom: 0.5rem !important;
}

.checkout-form .form-input,
.checkout-form .form-select {
    border: 2px solid var(--color-gray-300) !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
}

.checkout-form .form-input:focus,
.checkout-form .form-select:focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* Admin Panel Layout Fixes - Remove ALL Grid Conflicts */
.admin-layout {
    /* Remove grid display to prevent conflicts */
    display: block !important;
    min-height: 100vh !important;
    background: var(--color-gray-50) !important;
}

.admin-sidebar {
    background: var(--color-white) !important;
    border-right: 1px solid var(--color-gray-200) !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 280px !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
}

.admin-main {
    margin-left: 280px !important;
    padding: 1.5rem !important;
    min-height: 100vh !important;
    width: calc(100vw - 280px) !important;
    box-sizing: border-box !important;
    background: var(--color-gray-50) !important;
}

.dashboard-grid {
    /* Remove grid display to prevent conflicts */
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 1.5rem !important;
    margin-bottom: 2rem !important;
    width: 100% !important;
}

/* Remove grid from ALL admin components */
.admin-form-grid,
.products-grid,
.orders-grid,
.categories-grid,
.customers-grid,
.data-grid,
.content-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 1.5rem !important;
}

/* Ensure no grid displays in admin pages */
.admin-layout *[style*="display: grid"],
.admin-layout *[style*="grid-template-columns"],
.admin-layout div[style*="grid"],
.admin-layout section[style*="grid"],
.admin-layout main[style*="grid"] {
    display: flex !important;
    flex-wrap: wrap !important;
}

/* Force override any grid styles in admin */
.admin-layout * {
    grid-template-columns: none !important;
    grid-template-rows: none !important;
    grid-area: none !important;
}

/* Specific admin page overrides */
body.admin-page * {
    display: block !important;
}

body.admin-page .dashboard-grid,
body.admin-page .admin-form-grid,
body.admin-page .data-grid {
    display: flex !important;
    flex-wrap: wrap !important;
}

.dashboard-card {
    background: var(--color-white) !important;
    border-radius: 0.75rem !important;
    padding: 1.5rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid var(--color-gray-200) !important;
    transition: all 0.2s ease !important;
    flex: 1 1 280px !important;
    min-width: 280px !important;
    box-sizing: border-box !important;
}

.dashboard-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}

.dashboard-card-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 1rem !important;
}

.dashboard-card-title {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: var(--color-gray-600) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
}

.dashboard-card-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 1.125rem !important;
    color: var(--color-white) !important;
}

.dashboard-card-icon.primary {
    background: var(--color-primary) !important;
}

.dashboard-card-icon.success {
    background: var(--color-success) !important;
}

.dashboard-card-icon.warning {
    background: var(--color-warning) !important;
}

.dashboard-card-icon.error {
    background: var(--color-error) !important;
}

.dashboard-card-value {
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    color: var(--color-gray-900) !important;
    margin-bottom: 0.5rem !important;
    line-height: 1 !important;
}

.dashboard-card-change {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.dashboard-card-change.positive {
    color: var(--color-success) !important;
}

.dashboard-card-change.negative {
    color: var(--color-error) !important;
}

/* Admin page header fixes */
.admin-page-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 2rem !important;
    padding-bottom: 1.5rem !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
}

.admin-page-title {
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    color: var(--color-gray-900) !important;
    margin: 0 !important;
}

.admin-page-actions {
    display: flex !important;
    gap: 0.75rem !important;
}

/* Admin form fixes */
.admin-form {
    background: var(--color-white) !important;
    border-radius: 0.75rem !important;
    padding: 2rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid var(--color-gray-200) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.admin-form-grid {
    /* Remove grid display to prevent conflicts */
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 1.5rem !important;
}

/* Data table fixes */
.data-table-container {
    background: var(--color-white) !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid var(--color-gray-200) !important;
    overflow: hidden !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.data-table {
    width: 100% !important;
    border-collapse: collapse !important;
}

.data-table th {
    background: var(--color-gray-50) !important;
    padding: 1rem 1.5rem !important;
    text-align: left !important;
    font-weight: 600 !important;
    color: var(--color-gray-700) !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
}

.data-table td {
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
    color: var(--color-gray-900) !important;
}

.data-table tr:hover {
    background: var(--color-gray-50) !important;
}

/* Admin responsive fixes */
@media (max-width: 1024px) {
    .admin-layout {
        /* Remove grid references */
        display: block !important;
    }

    .admin-main {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .dashboard-grid {
        /* Keep flexbox for responsive */
        flex-direction: column !important;
    }

    .admin-form-grid {
        /* Keep flexbox for responsive */
        flex-direction: column !important;
    }

    .admin-page-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
    }
}
