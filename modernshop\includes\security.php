<?php
// Advanced Security Functions

// Rate limiting
class RateLimiter {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function checkRateLimit($identifier, $action, $limit = 5, $window = 300) {
        try {
            // Clean old entries
            $stmt = $this->pdo->prepare("DELETE FROM rate_limits WHERE created_at < ?");
            $stmt->execute([date('Y-m-d H:i:s', time() - $window)]);
            
            // Count current attempts
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count 
                FROM rate_limits 
                WHERE identifier = ? AND action = ? AND created_at > ?
            ");
            $stmt->execute([$identifier, $action, date('Y-m-d H:i:s', time() - $window)]);
            $count = $stmt->fetch()['count'];
            
            if ($count >= $limit) {
                return false;
            }
            
            // Record this attempt
            $stmt = $this->pdo->prepare("INSERT INTO rate_limits (identifier, action) VALUES (?, ?)");
            $stmt->execute([$identifier, $action]);
            
            return true;
        } catch (Exception $e) {
            // If rate limiting fails, allow the action but log the error
            error_log("Rate limiting error: " . $e->getMessage());
            return true;
        }
    }
}

// Input validation and sanitization
class InputValidator {
    
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function validatePassword($password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/', $password);
    }
    
    public static function validateUsername($username) {
        // 3-30 characters, alphanumeric and underscore only
        return preg_match('/^[a-zA-Z0-9_]{3,30}$/', $username);
    }
    
    public static function validatePhone($phone) {
        // Remove all non-digits
        $phone = preg_replace('/\D/', '', $phone);
        // Check if it's 10-15 digits
        return preg_match('/^\d{10,15}$/', $phone);
    }
    
    public static function validatePostalCode($code, $country = 'US') {
        switch ($country) {
            case 'US':
                return preg_match('/^\d{5}(-\d{4})?$/', $code);
            case 'CA':
                return preg_match('/^[A-Z]\d[A-Z] \d[A-Z]\d$/', $code);
            case 'UK':
                return preg_match('/^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/', $code);
            default:
                return strlen($code) >= 3 && strlen($code) <= 10;
        }
    }
    
    public static function sanitizeHtml($input) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    public static function sanitizeFilename($filename) {
        // Remove path traversal attempts and dangerous characters
        $filename = basename($filename);
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        return $filename;
    }
    
    public static function validateCreditCard($number) {
        // Remove spaces and dashes
        $number = preg_replace('/[\s-]/', '', $number);
        
        // Check if it's all digits and proper length
        if (!preg_match('/^\d{13,19}$/', $number)) {
            return false;
        }
        
        // Luhn algorithm
        $sum = 0;
        $length = strlen($number);
        
        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = intval($number[$i]);
            
            if (($length - $i) % 2 == 0) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }
        
        return $sum % 10 == 0;
    }
}

// Security headers
function setSecurityHeaders() {
    // Prevent clickjacking
    header('X-Frame-Options: DENY');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // XSS protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';");
    
    // HTTPS enforcement (uncomment in production with HTTPS)
    // header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
}

// SQL injection prevention
function prepareSafeQuery($pdo, $query, $params = []) {
    try {
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        throw new Exception("Database operation failed");
    }
}

// File upload security
function validateFileUpload($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = 5242880) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'No file uploaded or upload failed';
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > $maxSize) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    // Check file type
    $fileInfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($fileInfo, $file['tmp_name']);
    finfo_close($fileInfo);
    
    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($extension, $allowedTypes) || 
        !isset($allowedMimes[$extension]) || 
        $mimeType !== $allowedMimes[$extension]) {
        $errors[] = 'Invalid file type';
    }
    
    // Check for malicious content
    if (strpos(file_get_contents($file['tmp_name']), '<?php') !== false) {
        $errors[] = 'File contains potentially malicious content';
    }
    
    return $errors;
}

// Session security
function secureSession() {
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
    
    // Check for session hijacking
    if (!isset($_SESSION['user_agent'])) {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    } elseif ($_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        session_destroy();
        return false;
    }
    
    // Check for session fixation
    if (!isset($_SESSION['ip_address'])) {
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    } elseif ($_SESSION['ip_address'] !== ($_SERVER['REMOTE_ADDR'] ?? '')) {
        session_destroy();
        return false;
    }
    
    return true;
}

// Password hashing with salt
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

// Secure password verification
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Generate secure random token
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Encrypt sensitive data
function encryptData($data, $key) {
    $cipher = 'AES-256-GCM';
    $iv = random_bytes(16);
    $tag = '';
    
    $encrypted = openssl_encrypt($data, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
    
    return base64_encode($iv . $tag . $encrypted);
}

// Decrypt sensitive data
function decryptData($encryptedData, $key) {
    $cipher = 'AES-256-GCM';
    $data = base64_decode($encryptedData);
    
    $iv = substr($data, 0, 16);
    $tag = substr($data, 16, 16);
    $encrypted = substr($data, 32);
    
    return openssl_decrypt($encrypted, $cipher, $key, OPENSSL_RAW_DATA, $iv, $tag);
}

// Log security events
function logSecurityEvent($event, $details = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'user_id' => $_SESSION['user_id'] ?? null,
        'details' => $details
    ];
    
    error_log("SECURITY: " . json_encode($logEntry));
}

// Check for suspicious activity
function detectSuspiciousActivity() {
    $suspiciousPatterns = [
        'sql_injection' => '/(\bunion\b|\bselect\b|\binsert\b|\bdelete\b|\bdrop\b)/i',
        'xss_attempt' => '/<script|javascript:|on\w+\s*=/i',
        'path_traversal' => '/\.\.[\/\\\\]/i',
        'command_injection' => '/[;&|`$()]/i'
    ];
    
    $requestData = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($requestData as $key => $value) {
        if (is_string($value)) {
            foreach ($suspiciousPatterns as $type => $pattern) {
                if (preg_match($pattern, $value)) {
                    logSecurityEvent('suspicious_activity', [
                        'type' => $type,
                        'parameter' => $key,
                        'value' => $value
                    ]);
                    return true;
                }
            }
        }
    }
    
    return false;
}

// Initialize security measures
function initializeSecurity($pdo) {
    // Set security headers
    setSecurityHeaders();
    
    // Secure session
    if (!secureSession()) {
        return false;
    }
    
    // Check for suspicious activity
    if (detectSuspiciousActivity()) {
        http_response_code(403);
        exit('Suspicious activity detected');
    }
    
    // Initialize rate limiter
    $rateLimiter = new RateLimiter($pdo);
    
    return $rateLimiter;
}
?>
