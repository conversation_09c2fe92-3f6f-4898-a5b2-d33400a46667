<?php
// Authentication functions

// Register new user
function register_user($pdo, $username, $email, $password, $full_name) {
    try {
        // Check if username or email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new user
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name) VALUES (?, ?, ?, ?)");
        $result = $stmt->execute([$username, $email, $hashed_password, $full_name]);
        
        if ($result) {
            return ['success' => true, 'message' => 'Registration successful'];
        } else {
            return ['success' => false, 'message' => 'Registration failed'];
        }
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Login user
function login_user($pdo, $username, $password) {
    try {
        // Get user by username or email
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['full_name'] = $user['full_name'];
            
            return ['success' => true, 'message' => 'Login successful', 'user' => $user];
        } else {
            return ['success' => false, 'message' => 'Invalid username or password'];
        }
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Logout user
function logout_user() {
    // Clear all session variables
    $_SESSION = array();
    
    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy the session
    session_destroy();
    
    return ['success' => true, 'message' => 'Logged out successfully'];
}

// Validate registration input
function validate_registration($username, $email, $password, $confirm_password, $full_name) {
    $errors = [];
    
    // Username validation
    if (empty($username)) {
        $errors['username'] = 'Username is required';
    } elseif (strlen($username) < 3) {
        $errors['username'] = 'Username must be at least 3 characters';
    } elseif (strlen($username) > 50) {
        $errors['username'] = 'Username must be less than 50 characters';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $errors['username'] = 'Username can only contain letters, numbers, and underscores';
    }
    
    // Email validation
    if (empty($email)) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Please enter a valid email address';
    } elseif (strlen($email) > 100) {
        $errors['email'] = 'Email must be less than 100 characters';
    }
    
    // Full name validation
    if (empty($full_name)) {
        $errors['full_name'] = 'Full name is required';
    } elseif (strlen($full_name) < 2) {
        $errors['full_name'] = 'Full name must be at least 2 characters';
    } elseif (strlen($full_name) > 100) {
        $errors['full_name'] = 'Full name must be less than 100 characters';
    }
    
    // Password validation
    if (empty($password)) {
        $errors['password'] = 'Password is required';
    } elseif (strlen($password) < 6) {
        $errors['password'] = 'Password must be at least 6 characters';
    } elseif (strlen($password) > 255) {
        $errors['password'] = 'Password is too long';
    }
    
    // Confirm password validation
    if (empty($confirm_password)) {
        $errors['confirm_password'] = 'Please confirm your password';
    } elseif ($password !== $confirm_password) {
        $errors['confirm_password'] = 'Passwords do not match';
    }
    
    return $errors;
}

// Validate login input
function validate_login($username, $password) {
    $errors = [];
    
    if (empty($username)) {
        $errors['username'] = 'Username or email is required';
    }
    
    if (empty($password)) {
        $errors['password'] = 'Password is required';
    }
    
    return $errors;
}

// Check password strength
function check_password_strength($password) {
    $strength = 0;
    $feedback = [];
    
    // Length check
    if (strlen($password) >= 8) {
        $strength += 25;
    } else {
        $feedback[] = 'Use at least 8 characters';
    }
    
    // Lowercase check
    if (preg_match('/[a-z]/', $password)) {
        $strength += 25;
    } else {
        $feedback[] = 'Include lowercase letters';
    }
    
    // Uppercase check
    if (preg_match('/[A-Z]/', $password)) {
        $strength += 25;
    } else {
        $feedback[] = 'Include uppercase letters';
    }
    
    // Number or special character check
    if (preg_match('/[0-9]/', $password) || preg_match('/[^a-zA-Z0-9]/', $password)) {
        $strength += 25;
    } else {
        $feedback[] = 'Include numbers or special characters';
    }
    
    // Determine strength level
    if ($strength < 50) {
        $level = 'weak';
    } elseif ($strength < 75) {
        $level = 'fair';
    } elseif ($strength < 100) {
        $level = 'good';
    } else {
        $level = 'strong';
    }
    
    return [
        'strength' => $strength,
        'level' => $level,
        'feedback' => $feedback
    ];
}

// Require login (redirect if not logged in)
function require_login($redirect_url = 'login.php') {
    if (!is_logged_in()) {
        set_flash_message('Please log in to access this page', 'warning');
        redirect($redirect_url);
    }
}

// Require admin (redirect if not admin)
function require_admin($redirect_url = 'index.php') {
    if (!is_admin()) {
        set_flash_message('Access denied. Admin privileges required.', 'error');
        redirect($redirect_url);
    }
}

// Get user info
function get_user_info($pdo, $user_id) {
    try {
        $stmt = $pdo->prepare("SELECT id, username, email, full_name, role, created_at FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return false;
    }
}

// Update user profile
function update_user_profile($pdo, $user_id, $full_name, $email) {
    try {
        // Check if email is already taken by another user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $user_id]);
        
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Email is already taken by another user'];
        }
        
        // Update user profile
        $stmt = $pdo->prepare("UPDATE users SET full_name = ?, email = ? WHERE id = ?");
        $result = $stmt->execute([$full_name, $email, $user_id]);
        
        if ($result) {
            return ['success' => true, 'message' => 'Profile updated successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to update profile'];
        }
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Change password
function change_password($pdo, $user_id, $current_password, $new_password) {
    try {
        // Get current password hash
        $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($current_password, $user['password'])) {
            return ['success' => false, 'message' => 'Current password is incorrect'];
        }
        
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $result = $stmt->execute([$hashed_password, $user_id]);
        
        if ($result) {
            return ['success' => true, 'message' => 'Password changed successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to change password'];
        }
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}
?>
